#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import urllib.parse
import json
import requests
from typing import Dict, List

class RealDataExtractor:
    """从XML抓包数据中提取真实的数据值"""
    
    def __init__(self):
        self.real_api_base = "https://sys.wyzxcn.com/jeecg-boot"
        self.real_data_samples = {}
        
    def extract_real_data_from_urls(self):
        """从抓包的URL中提取真实数据"""
        print("🔍 从XML抓包URL中提取真实数据...")
        print("=" * 80)
        
        # 从分析结果中找到的真实数据URL
        real_data_urls = [
            # 真实的电池包信息
            "https://sys.wyzxcn.com/jeecg-boot/fnjbattery/parseAlarmAndBattery?batteryPackageInfo=%7B%22infoTime%22%3A%22250618142903%22%2C%22batteryId%22%3A%22BT2072055010032504140408%22%2C%22batteryId26%22%3A%22BT2072055010032504140408%22%2C%22totalVoltage%22%3A%2284.40%22%2C%22totalCurrent%22%3A%220.00%22%2C%22soc%22%3A%2296%22%2C%22batteryStatus%22%3A%7B%22eDetectOpen%22%3A%220%22%2C%22dischargeOverCurrent%22%3A%220%22%2C%22eCoreTempHigh%22%3A%220%22%2C%22tempDetectOpenCircuit%22%3A%220%22%2C%22rent%22%3A%220%22%2C%22BMSPowerDown%22%3A%220%22%2C%22chargeOverCurrent%22%3A%220%22%2C%22DCMOS%22%3A%221%22%2C%22validBCode%22%3A%220%22%2C%22chargeMOSStatus%22%3A%221%22%2C%22eCoreTempUnder%22%3A%220%22%2C%22BMSTempOver%22%3A%220%22%2C%22BMSFailureStatus%22%3A%220%22%2C%22discharge%22%3A%220%22%2C%22eCoreTempOver%22%3A%220%22%2C%22BMSTempHigh%22%3A%220%22%2C%22eDetectOpenCircuit%22%3A%220%22%2C%22eCoreOverVol%22%3A%220%22%2C%22charge%22%3A%220%22%2C%22BMSStandMode%22%3A%220%22%2C%22BMSPowerDownMode%22%3A%220%22%2C%22cOverCur%22%3A%220%22%2C%22chargeMOS%22%3A%221%22%2C%22forbidDCDuration%22%3A%220%22%2C%22eCoreTempLow%22%3A%220%22%2C%22shortCut%22%3A%220%22%2C%22forbidDischarge%22%3A%220%22%2C%22fillUp%22%3A%220%22%2C%22dcOverCur%22%3A%220%22%2C%22forbidCharge%22%3A%220%22%2C%22eCoreUnderVol%22%3A%220%22%2C%22BMSFailure%22%3A%220%22%2C%22forbidDC%22%3A%220%22%2C%22BMSStand%22%3A%220%22%2C%22permitDCDuration%22%3A%220%22%2C%22tempDetectOpen%22%3A%220%22%2C%22dc%22%3A%220%22%2C%22disChargeMOSStatus%22%3A%221%22%7D%2C%22cellQuantity%22%3A%2220%22%2C%22cellVoltageDetail%22%3A%5B%224.222%22%2C%224.223%22%2C%224.223%22%2C%224.224%22%2C%224.223%22%2C%224.221%22%2C%224.224%22%2C%224.225%22%2C%224.223%22%2C%224.225%22%2C%224.220%22%2C%224.223%22%2C%224.224%22%2C%224.222%22%2C%224.223%22%2C%224.224%22%2C%224.223%22%2C%224.223%22%2C%224.223%22%2C%224.222%22%5D%2C%22tempQuantity%22%3A%222%22%2C%22tempDetailInfo%22%3A%5B%2234.00%22%2C%2233.00%22%5D%2C%22BMSTemp%22%3A%2235%22%2C%22residualCapacity%22%3A%2251.30%22%2C%22currentCapacity%22%3A%2253.00%22%2C%22loopTimes%22%3A%221%22%2C%22sMaxVol%22%3A%2211.68%22%2C%22sMinVol%22%3A%2212.36%22%2C%22sMaxSeriesNum%22%3A%2225%22%2C%22sMinSeriesNum%22%3A%2229%22%2C%22soh%22%3A%220%22%2C%22lifeSignal%22%3A%2278%22%7D",
            
            # 真实的BMS状态信息
            "https://sys.wyzxcn.com/jeecg-boot/fnjbattery/parseProtectionAndWarning?bmsStatusInfo=%7B%22infoTime%22%3A%22250618142903%22%2C%22envTemp%22%3A%2236%22%2C%22alertStatusBit%22%3A%220000000000000000%22%2C%22protectStatusBit%22%3A%220000000000000000%22%2C%22chargeMosStatus%22%3A1%2C%22invalidStatusBit%22%3A%2200000000%22%2C%22disChargeMosStatus%22%3A1%2C%22bmsStatusBit%22%3A%2200000000%22%2C%22balanceStatus1Bit%22%3A%220000110110000111%22%2C%22balanceStatus2Bit%22%3A%220000000010101000%22%2C%22averageVol%22%3A%220.001%22%2C%22volDiffer%22%3A%220.862%22%2C%22totalMileage%22%3A%2218.2%22%2C%22comStatusBit%22%3A%2200000011%22%2C%22remoteUpdate%22%3A%220%22%2C%22downProgress%22%3A%220%22%2C%22updateProgress%22%3A%220%22%2C%22codeAsccII%22%3A%222%2C0%2C%2C0%2C111%2C0%22%2C%22permitDischargeDurationCountDown%22%3A%2264241%22%2C%22forbidDischargeDurationCountDown%22%3A%224300%22%2C%22totalSumChargeTimes%22%3A%224250%22%2C%22lastChargeInterval%22%3A%224350%22%2C%22chargeOrDischargeHighTempTimes%22%3A%224250%22%2C%22chargeOrDischargeLowTempTimes%22%3A%22860%22%2C%22forceOverDischargeTimes%22%3A%22840%22%2C%22forceOverschargeTimes%22%3A%223000%22%2C%22forceOverCurrentTimes%22%3A%223150%22%2C%22forceShortCircleTimes%22%3A%222500%22%2C%22lowVolPowerOffTimes%22%3A%222800%22%2C%22exceptionShutDownTimes%22%3A%22600%22%2C%22forceResetTimes%22%3A%229999%22%2C%22totalSumDischargeTime%22%3A%222700%22%2C%22totalSumchargeTime%22%3A%220%22%2C%22CCID%22%3A%2289860406192490031343%22%2C%22IEMI%22%3A%22460042639021943%22%2C%22DTU%22%3A%22BMS_F24S3TC_V3_00_17%22%2C%22BMSSV%22%3A%223023%22%2C%22BMSHV%22%3A%222%22%2C%22protectFlag%22%3A%2200000000000000000000%22%2C%22alertFlag%22%3A%2200008001001000000000%22%7D"
        ]
        
        # 解析每个URL中的数据
        for i, url in enumerate(real_data_urls, 1):
            print(f"\n📊 解析真实数据样本 {i}:")
            self.parse_url_data(url)
    
    def parse_url_data(self, url: str):
        """解析URL中的数据参数"""
        try:
            # 解析URL参数
            parsed_url = urllib.parse.urlparse(url)
            params = urllib.parse.parse_qs(parsed_url.query)
            
            # 提取电池包信息
            if 'batteryPackageInfo' in params:
                battery_info_encoded = params['batteryPackageInfo'][0]
                battery_info_decoded = urllib.parse.unquote(battery_info_encoded)
                battery_data = json.loads(battery_info_decoded)
                
                print("  🔋 真实电池包信息:")
                self.print_battery_data(battery_data)
                self.real_data_samples['battery_package'] = battery_data
            
            # 提取BMS状态信息
            if 'bmsStatusInfo' in params:
                bms_info_encoded = params['bmsStatusInfo'][0]
                bms_info_decoded = urllib.parse.unquote(bms_info_encoded)
                bms_data = json.loads(bms_info_decoded)
                
                print("  ⚡ 真实BMS状态信息:")
                self.print_bms_data(bms_data)
                self.real_data_samples['bms_status'] = bms_data
                
        except Exception as e:
            print(f"  ❌ 解析失败: {e}")
    
    def print_battery_data(self, data: dict):
        """打印电池数据"""
        key_fields = {
            'totalVoltage': '总电压',
            'totalCurrent': '总电流',
            'soc': 'SOC电量',
            'BMSTemp': 'BMS温度',
            'cellQuantity': '电芯数量',
            'tempQuantity': '温度传感器数量',
            'residualCapacity': '剩余容量',
            'currentCapacity': '当前容量'
        }
        
        for field, desc in key_fields.items():
            if field in data:
                print(f"    {desc}: {data[field]}")
        
        # 打印单体电压
        if 'cellVoltageDetail' in data:
            voltages = data['cellVoltageDetail']
            print(f"    单体电压 ({len(voltages)}个): {voltages[:5]}... (显示前5个)")
            
        # 打印温度信息
        if 'tempDetailInfo' in data:
            temps = data['tempDetailInfo']
            print(f"    温度传感器: {temps}")
    
    def print_bms_data(self, data: dict):
        """打印BMS数据"""
        key_fields = {
            'envTemp': '环境温度',
            'chargeMosStatus': '充电MOS状态',
            'disChargeMosStatus': '放电MOS状态',
            'averageVol': '平均电压',
            'volDiffer': '电压差',
            'totalMileage': '总里程',
            'alertStatusBit': '警告状态位',
            'protectStatusBit': '保护状态位'
        }
        
        for field, desc in key_fields.items():
            if field in data:
                print(f"    {desc}: {data[field]}")
    
    def compare_with_current_web_data(self):
        """对比当前Web数据与真实数据"""
        print("\n" + "=" * 80)
        print("⚖️ 数据一致性对比分析")
        print("=" * 80)
        
        try:
            # 获取当前Web数据
            response = requests.get(
                'http://127.0.0.1:5000/api/battery/realtime',
                params={
                    'key': '79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c',
                    'clientId': '380074209785'
                }
            )
            
            if response.status_code == 200:
                web_data = response.json()
                detail = json.loads(web_data['detail'])
                battery_info = json.loads(detail['batteryPackageInfo'])
                bms_info = json.loads(detail['bmsStatusInfo'])
                
                print("\n📱 当前Web数据 vs 🔍 真实数据对比:")
                
                if 'battery_package' in self.real_data_samples:
                    real_battery = self.real_data_samples['battery_package']
                    self.compare_battery_data(battery_info, real_battery)
                
                if 'bms_status' in self.real_data_samples:
                    real_bms = self.real_data_samples['bms_status']
                    self.compare_bms_data(bms_info, real_bms)
                    
            else:
                print(f"❌ 无法获取当前Web数据: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 对比失败: {e}")
    
    def compare_battery_data(self, web_data: dict, real_data: dict):
        """对比电池数据"""
        print("\n🔋 电池数据对比:")
        
        comparisons = [
            ('totalVoltage', '总电压', 'V'),
            ('totalCurrent', '总电流', 'A'),
            ('soc', 'SOC电量', '%'),
            ('BMSTemp', 'BMS温度', '°C'),
            ('residualCapacity', '剩余容量', 'Ah'),
            ('currentCapacity', '当前容量', 'Ah')
        ]
        
        for field, desc, unit in comparisons:
            web_val = web_data.get(field, '--')
            real_val = real_data.get(field, '--')
            
            # 判断是否一致
            status = "✅" if str(web_val) == str(real_val) else "❌"
            
            print(f"  {status} {desc}: Web={web_val}{unit} | 真实={real_val}{unit}")
        
        # 对比单体电压
        web_voltages = web_data.get('cellVoltageDetail', [])
        real_voltages = real_data.get('cellVoltageDetail', [])
        
        if web_voltages and real_voltages:
            web_count = len(web_voltages)
            real_count = len(real_voltages)
            status = "✅" if web_count == real_count else "❌"
            print(f"  {status} 单体电压数量: Web={web_count}个 | 真实={real_count}个")
            
            # 对比电压范围
            if web_voltages and real_voltages:
                web_range = f"{min(web_voltages)}-{max(web_voltages)}"
                real_range = f"{min(real_voltages)}-{max(real_voltages)}"
                print(f"    电压范围: Web={web_range}V | 真实={real_range}V")
    
    def compare_bms_data(self, web_data: dict, real_data: dict):
        """对比BMS数据"""
        print("\n⚡ BMS数据对比:")
        
        comparisons = [
            ('envTemp', '环境温度', '°C'),
            ('chargeMosStatus', '充电MOS状态', ''),
            ('disChargeMosStatus', '放电MOS状态', ''),
            ('totalMileage', '总里程', 'km'),
            ('volDiffer', '电压差', 'V'),
            ('alertStatusBit', '警告状态位', ''),
            ('protectStatusBit', '保护状态位', '')
        ]
        
        for field, desc, unit in comparisons:
            web_val = web_data.get(field, '--')
            real_val = real_data.get(field, '--')
            
            # 判断是否一致
            status = "✅" if str(web_val) == str(real_val) else "❌"
            
            print(f"  {status} {desc}: Web={web_val}{unit} | 真实={real_val}{unit}")
    
    def generate_correction_recommendations(self):
        """生成数据修正建议"""
        print("\n" + "=" * 80)
        print("🔧 数据修正建议")
        print("=" * 80)
        
        if 'battery_package' in self.real_data_samples:
            real_battery = self.real_data_samples['battery_package']
            print("\n📋 需要修正的电池数据:")
            print(f"  • 总电压: 应为 {real_battery.get('totalVoltage')}V")
            print(f"  • 总电流: 应为 {real_battery.get('totalCurrent')}A")
            print(f"  • SOC电量: 应为 {real_battery.get('soc')}%")
            print(f"  • BMS温度: 应为 {real_battery.get('BMSTemp')}°C")
            print(f"  • 剩余容量: 应为 {real_battery.get('residualCapacity')}Ah")
            print(f"  • 当前容量: 应为 {real_battery.get('currentCapacity')}Ah")
            
            # 单体电压
            if 'cellVoltageDetail' in real_battery:
                voltages = real_battery['cellVoltageDetail']
                print(f"  • 单体电压: 应为 {len(voltages)}个，范围 {min(voltages)}-{max(voltages)}V")
        
        if 'bms_status' in self.real_data_samples:
            real_bms = self.real_data_samples['bms_status']
            print(f"\n📋 需要修正的BMS数据:")
            print(f"  • 环境温度: 应为 {real_bms.get('envTemp')}°C")
            print(f"  • 总里程: 应为 {real_bms.get('totalMileage')}km")
            print(f"  • 电压差: 应为 {real_bms.get('volDiffer')}V")
            print(f"  • 平均电压: 应为 {real_bms.get('averageVol')}V")

def main():
    extractor = RealDataExtractor()
    extractor.extract_real_data_from_urls()
    extractor.compare_with_current_web_data()
    extractor.generate_correction_recommendations()

if __name__ == "__main__":
    main()

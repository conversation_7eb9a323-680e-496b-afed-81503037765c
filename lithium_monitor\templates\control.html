{% extends "base.html" %}

{% block title %}设备控制 - 锂电监控系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 页面标题 -->
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-sliders-h me-2"></i>设备控制面板</h2>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" id="refreshStatus">
                    <i class="fas fa-sync-alt me-1"></i>刷新状态
                </button>
                <button type="button" class="btn btn-outline-secondary" id="viewHistory">
                    <i class="fas fa-history me-1"></i>控制历史
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 当前状态显示 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>当前MOS状态
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-6 mb-3">
                        <div class="border rounded p-4">
                            <h4 class="mb-3">充电MOS</h4>
                            <div class="mb-3">
                                <span class="badge fs-6 p-2" id="currentChargeMosStatus">--</span>
                            </div>
                            <small class="text-muted">控制电池充电回路的开关状态</small>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="border rounded p-4">
                            <h4 class="mb-3">放电MOS</h4>
                            <div class="mb-3">
                                <span class="badge fs-6 p-2" id="currentDischargeMosStatus">--</span>
                            </div>
                            <small class="text-muted">控制电池放电回路的开关状态</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 控制操作面板 -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-gamepad me-2"></i>MOS控制操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- 放电MOS控制 -->
                    <div class="col-md-6 mb-4">
                        <div class="border rounded p-3">
                            <h6 class="mb-3">
                                <i class="fas fa-battery-empty me-2"></i>放电MOS控制
                            </h6>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-success" 
                                        data-cmd-id="41" data-bs-toggle="tooltip" 
                                        title="允许电池向外部负载放电">
                                    <i class="fas fa-play me-2"></i>打开放电MOS
                                </button>
                                <button type="button" class="btn btn-danger" 
                                        data-cmd-id="40" data-bs-toggle="tooltip" 
                                        title="禁止电池向外部负载放电">
                                    <i class="fas fa-stop me-2"></i>关闭放电MOS
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 充放电MOS同时控制 -->
                    <div class="col-md-6 mb-4">
                        <div class="border rounded p-3">
                            <h6 class="mb-3">
                                <i class="fas fa-battery-full me-2"></i>充放电MOS同时控制
                            </h6>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-primary" 
                                        data-cmd-id="08" data-bs-toggle="tooltip" 
                                        title="同时打开充电和放电MOS">
                                    <i class="fas fa-power-off me-2"></i>打开充/放电MOS
                                </button>
                                <button type="button" class="btn btn-warning" 
                                        data-cmd-id="09" data-bs-toggle="tooltip" 
                                        title="同时关闭充电和放电MOS">
                                    <i class="fas fa-ban me-2"></i>关闭充/放电MOS
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 安全提示 -->
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>安全提示：</strong>
                    请确认操作的必要性和安全性。MOS开关控制会直接影响电池的充放电功能，
                    错误操作可能导致设备无法正常工作。
                </div>
            </div>
        </div>
    </div>
    
    <!-- 控制历史和状态 -->
    <div class="col-lg-4">
        <!-- 最近控制记录 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>最近控制记录
                </h5>
            </div>
            <div class="card-body">
                <div id="recentControls">
                    <div class="text-center text-muted">
                        <i class="fas fa-history fa-2x mb-2"></i>
                        <div>暂无控制记录</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 设备信息 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-microchip me-2"></i>设备信息
                </h5>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="d-flex justify-content-between mb-2">
                        <span>客户端ID:</span>
                        <span class="text-muted" id="clientId">--</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>设备密钥:</span>
                        <span class="text-muted" id="deviceKey">****</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>连接状态:</span>
                        <span class="text-success" id="connectionStatus">
                            <i class="fas fa-circle me-1"></i>已连接
                        </span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>最后心跳:</span>
                        <span class="text-muted" id="lastHeartbeat">--</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 确认对话框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">确定要执行此操作吗？</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    此操作将立即生效，请确认设备当前状态适合执行此操作。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmExecute">确认执行</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentCmdId = null;
let statusUpdateInterval;

$(document).ready(function() {
    loadCurrentStatus();
    loadControlHistory();
    loadDeviceInfo();
    startStatusUpdate();
    
    // 绑定控制按钮事件
    $('[data-cmd-id]').click(function() {
        const cmdId = $(this).data('cmd-id');
        const buttonText = $(this).text().trim();
        showConfirmDialog(cmdId, buttonText);
    });
    
    // 绑定其他事件
    $('#refreshStatus').click(loadCurrentStatus);
    $('#viewHistory').click(showFullHistory);
    $('#confirmExecute').click(executeControl);
});

function loadCurrentStatus() {
    $.ajax({
        url: '/api/control/mos-status',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                updateStatusDisplay(response.result);
            }
        },
        error: function() {
            showNotification('获取状态失败', 'error');
        }
    });
}

function updateStatusDisplay(status) {
    // 更新充电MOS状态
    const chargeMosElement = $('#currentChargeMosStatus');
    if (status.chargeMOS === 1) {
        chargeMosElement.removeClass('bg-danger').addClass('bg-success').text('开启');
    } else {
        chargeMosElement.removeClass('bg-success').addClass('bg-danger').text('关闭');
    }
    
    // 更新放电MOS状态
    const dischargeMosElement = $('#currentDischargeMosStatus');
    if (status.dischargeMOS === 1) {
        dischargeMosElement.removeClass('bg-danger').addClass('bg-success').text('开启');
    } else {
        dischargeMosElement.removeClass('bg-success').addClass('bg-danger').text('关闭');
    }
    
    // 更新最后更新时间
    $('#lastHeartbeat').text(new Date(status.last_update * 1000).toLocaleString());
}

function loadControlHistory() {
    $.ajax({
        url: '/api/control/history',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                updateHistoryDisplay(response.result.records.slice(0, 5)); // 只显示最近5条
            }
        },
        error: function() {
            console.log('获取控制历史失败');
        }
    });
}

function updateHistoryDisplay(records) {
    const container = $('#recentControls');
    
    if (records.length === 0) {
        container.html(`
            <div class="text-center text-muted">
                <i class="fas fa-history fa-2x mb-2"></i>
                <div>暂无控制记录</div>
            </div>
        `);
        return;
    }
    
    let html = '';
    records.forEach(record => {
        const time = new Date(record.timestamp).toLocaleString();
        const statusClass = record.status === 'success' ? 'text-success' : 'text-danger';
        const statusIcon = record.status === 'success' ? 'fa-check' : 'fa-times';
        
        html += `
            <div class="border-bottom pb-2 mb-2">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="fw-bold">${record.command}</div>
                        <small class="text-muted">${time}</small>
                    </div>
                    <i class="fas ${statusIcon} ${statusClass}"></i>
                </div>
            </div>
        `;
    });
    
    container.html(html);
}

function loadDeviceInfo() {
    $.ajax({
        url: '/api/config',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                $('#clientId').text(response.result.client_id);
                $('#deviceKey').text('****' + response.result.client_id.slice(-4));
            }
        }
    });
}

function showConfirmDialog(cmdId, buttonText) {
    currentCmdId = cmdId;
    
    const commandDescriptions = {
        '40': '关闭放电MOS - 将禁止电池向外部负载放电',
        '41': '打开放电MOS - 将允许电池向外部负载放电',
        '08': '打开充/放电MOS - 将同时开启充电和放电功能',
        '09': '关闭充/放电MOS - 将同时关闭充电和放电功能'
    };
    
    const message = commandDescriptions[cmdId] || `执行操作: ${buttonText}`;
    $('#confirmMessage').text(message);
    
    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    modal.show();
}

function executeControl() {
    if (!currentCmdId) return;
    
    showLoading();
    
    $.ajax({
        url: '/api/control/terminal',
        method: 'GET',
        data: {
            key: '79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c',
            clientId: '380074209785',
            cmdId: currentCmdId
        },
        success: function(response) {
            if (response.success) {
                showNotification('控制命令执行成功', 'success');
                
                // 关闭确认对话框
                const modal = bootstrap.Modal.getInstance(document.getElementById('confirmModal'));
                modal.hide();
                
                // 刷新状态和历史记录
                setTimeout(() => {
                    loadCurrentStatus();
                    loadControlHistory();
                }, 1000);
                
            } else {
                showNotification('控制命令执行失败: ' + response.message, 'error');
            }
        },
        error: function() {
            showNotification('网络连接错误', 'error');
        },
        complete: function() {
            hideLoading();
            currentCmdId = null;
        }
    });
}

function startStatusUpdate() {
    statusUpdateInterval = setInterval(loadCurrentStatus, 10000); // 每10秒更新一次状态
}

function stopStatusUpdate() {
    if (statusUpdateInterval) {
        clearInterval(statusUpdateInterval);
    }
}

function showFullHistory() {
    showNotification('完整历史记录功能开发中...', 'info');
}

// 页面卸载时停止状态更新
$(window).on('beforeunload', function() {
    stopStatusUpdate();
});
</script>
{% endblock %}

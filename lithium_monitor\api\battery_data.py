#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import json
import urllib.parse
from flask import Blueprint, request, jsonify, current_app
from api.auth import require_auth

# 创建电池数据蓝图
battery_data_bp = Blueprint('battery_data', __name__)

@battery_data_bp.route('/models', methods=['GET'])
def get_battery_models():
    """获取电池型号列表 - 对应 /jeecg-boot/wybattery/wyBatteryInfor/list"""

    # 使用真实数据模拟器
    from utils.data_simulator import real_battery_simulator
    models = real_battery_simulator.generate_battery_models()

    return jsonify({
        'success': True,
        'message': '',
        'code': 200,
        'result': {
            'records': models,
            'total': len(models),
            'size': 10,
            'current': 1,
            'orders': [],
            'optimizeCountSql': True,
            'searchCount': True,
            'maxLimit': None,
            'countId': None,
            'pages': 1
        },
        'timestamp': int(time.time() * 1000)
    })

@battery_data_bp.route('/user-batteries', methods=['GET'])
def get_user_batteries():
    """获取用户电池列表 - 对应 /jeecg-boot/battery/userBattery/api/list"""

    user_id = request.args.get('userId', current_app.config['BATTERY_CONFIG']['DEFAULT_USER_ID'])

    # 使用真实数据模拟器
    from utils.data_simulator import real_battery_simulator
    user_batteries = real_battery_simulator.generate_user_battery_list(user_id)

    return jsonify({
        'success': True,
        'message': '查询成功',
        'code': 200,
        'result': user_batteries,
        'timestamp': int(time.time() * 1000)
    })

@battery_data_bp.route('/realtime', methods=['GET'])
def get_realtime_data():
    """获取实时电池数据 - 对应 /jeecg-boot/fnjbattery/realTime"""

    key = request.args.get('key', current_app.config['BATTERY_CONFIG']['DEFAULT_KEY'])
    client_id = request.args.get('clientId', current_app.config['BATTERY_CONFIG']['DEFAULT_CLIENT_ID'])

    # 验证参数
    if not key or not client_id:
        return jsonify({
            'code': 400,
            'message': '缺少必要参数',
            'retain': None,
            'data': None,
            'detail': None,
            'dataObj': None
        }), 400

    # 使用真实数据模拟器
    from utils.data_simulator import real_battery_simulator
    realtime_data = real_battery_simulator.generate_realtime_data()

    return jsonify({
        'code': 200,
        'message': '返回信息格式非json格式',
        'retain': None,
        'data': None,
        'detail': json.dumps(realtime_data, ensure_ascii=False),
        'dataObj': None
    })

def get_static_realtime_data():
    """获取静态实时数据（用于模拟器未创建时）"""
    return {
        'mobile': current_app.config['BATTERY_CONFIG']['DEFAULT_CLIENT_ID'],
        'heartBeatTime': '250618143708',
        'latitude': '22.506839',
        'longitude': '113.417564',
        'altitude': '3',
        'speed': '0',
        'direction': '164',
        'time': '250618143708',
        'batteryId': current_app.config['BATTERY_CONFIG']['DEFAULT_BATTERY_ID'],
        'batteryId26': current_app.config['BATTERY_CONFIG']['DEFAULT_BATTERY_ID'],
        'bmsVersion': '22',
        'batteryPackageInfo': json.dumps({
            'infoTime': '250618142903',
            'batteryId': current_app.config['BATTERY_CONFIG']['DEFAULT_BATTERY_ID'],
            'totalVoltage': '84.40',
            'totalCurrent': '0.00',
            'soc': '96',
            'cellQuantity': '20',
            'cellVoltageDetail': ['4.222', '4.223', '4.223', '4.224', '4.223', 
                                '4.221', '4.224', '4.225', '4.223', '4.225',
                                '4.220', '4.223', '4.224', '4.222', '4.223',
                                '4.224', '4.223', '4.223', '4.223', '4.222'],
            'tempQuantity': '2',
            'tempDetailInfo': ['34.00', '33.00'],
            'BMSTemp': '35',
            'residualCapacity': '51.30',
            'currentCapacity': '53.00',
            'batteryStatus': {
                'chargeMOSStatus': '1',
                'disChargeMOSStatus': '1',
                'DCMOS': '1',
                'chargeMOS': '1'
            }
        }, ensure_ascii=False)
    }

@battery_data_bp.route('/device-parameters', methods=['GET'])
def get_device_parameters():
    """获取设备参数 - 对应 /jeecg-boot/fnjbattery/deviceParameters"""

    client_id = request.args.get('clientId', current_app.config['BATTERY_CONFIG']['DEFAULT_CLIENT_ID'])
    key = request.args.get('key', current_app.config['BATTERY_CONFIG']['DEFAULT_KEY'])

    # 验证参数
    if not client_id or not key:
        return jsonify({
            'code': 400,
            'message': '缺少必要参数',
            'retain': None,
            'data': None,
            'detail': None,
            'dataObj': None
        }), 400

    # 使用真实数据模拟器
    from utils.data_simulator import real_battery_simulator
    parameters = real_battery_simulator.generate_device_parameters()

    return jsonify({
        'code': 200,
        'message': '返回信息格式非json格式',
        'retain': None,
        'data': None,
        'detail': json.dumps(parameters, ensure_ascii=False),
        'dataObj': None
    })

@battery_data_bp.route('/protection-warning', methods=['GET'])
def parse_protection_warning():
    """解析保护和警告信息 - 对应 /jeecg-boot/fnjbattery/parseProtectionAndWarning"""

    bms_status_info = request.args.get('bmsStatusInfo', '{}')

    # 使用真实警报模拟器解析
    from utils.data_simulator import real_alert_simulator
    result = real_alert_simulator.parse_protection_and_warning(bms_status_info)

    return jsonify({
        'success': True,
        'message': '',
        'code': 200,
        'result': result,
        'timestamp': int(time.time() * 1000)
    })

@battery_data_bp.route('/parse-alarm-battery', methods=['GET'])
def parse_alarm_and_battery():
    """解析警报和电池信息 - 对应 /jeecg-boot/fnjbattery/parseAlarmAndBattery"""

    bms_status_info = request.args.get('bmsStatusInfo', '{}')
    battery_package_info = request.args.get('batteryPackageInfo', '{}')

    # 使用真实警报模拟器解析
    from utils.data_simulator import real_alert_simulator

    # 解析警报信息
    alarm_result = real_alert_simulator.parse_protection_and_warning(bms_status_info)

    # 解析电池信息
    try:
        battery_data = json.loads(urllib.parse.unquote(battery_package_info))
        battery_result = {
            'voltage': battery_data.get('totalVoltage', '0'),
            'current': battery_data.get('totalCurrent', '0'),
            'soc': battery_data.get('soc', '0'),
            'temperature': battery_data.get('BMSTemp', '0')
        }
    except:
        battery_result = {
            'voltage': '0',
            'current': '0',
            'soc': '0',
            'temperature': '0'
        }

    return jsonify({
        'success': True,
        'message': '',
        'code': 200,
        'result': {
            'alarms': alarm_result,
            'battery': battery_result
        },
        'timestamp': int(time.time() * 1000)
    })

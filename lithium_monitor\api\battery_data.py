#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import json
from flask import Blueprint, request, jsonify, current_app
from api.auth import require_auth

# 创建电池数据蓝图
battery_data_bp = Blueprint('battery_data', __name__)

@battery_data_bp.route('/models', methods=['GET'])
def get_battery_models():
    """获取电池型号列表 - 对应 /jeecg-boot/wybattery/wyBatteryInfor/list"""
    
    models = []
    for model, specs in current_app.config['BATTERY_MODELS'].items():
        models.append({
            'id': f"model_{model.replace('V', 'v').replace('Ah', 'ah')}",
            'model': model,
            'sign': f"高性能{model}电池",
            'voltage': specs['voltage'],
            'chargeCurrent': specs['charge_current'],
            'dischargeCurrent': specs['discharge_current'],
            'size': specs['size'],
            'weight': specs['weight'],
            'waterproofRating': specs['waterproof'],
            'type': '锂离子动力电池',
            'image': f"temp/{model}_battery.png"
        })
    
    return jsonify({
        'success': True,
        'message': '',
        'code': 200,
        'result': {
            'records': models,
            'total': len(models),
            'size': 10,
            'current': 1,
            'pages': 1
        },
        'timestamp': int(time.time() * 1000)
    })

@battery_data_bp.route('/user-batteries', methods=['GET'])
def get_user_batteries():
    """获取用户电池列表 - 对应 /jeecg-boot/battery/userBattery/api/list"""
    
    user_id = request.args.get('userId', current_app.config['BATTERY_CONFIG']['DEFAULT_USER_ID'])
    
    # 模拟用户电池数据
    user_batteries = [{
        'userBattery': {
            'id': '1934498155206926338',
            'userId': user_id,
            'clientId': current_app.config['BATTERY_CONFIG']['DEFAULT_CLIENT_ID'],
            'batteryCode': current_app.config['BATTERY_CONFIG']['DEFAULT_BATTERY_ID'],
            'batteryType': '72V55Ah',
            'name': '我的锂电池设备',
            'owner': user_id,
            'createTime': '2025-06-18 14:27:56'
        },
        'batteryImg': 'temp/72V55Ah_battery.png'
    }]
    
    return jsonify({
        'success': True,
        'message': '查询成功',
        'code': 200,
        'result': user_batteries,
        'timestamp': int(time.time() * 1000)
    })

@battery_data_bp.route('/realtime', methods=['GET'])
def get_realtime_data():
    """获取实时电池数据 - 对应 /jeecg-boot/fnjbattery/realTime"""
    
    key = request.args.get('key', current_app.config['BATTERY_CONFIG']['DEFAULT_KEY'])
    client_id = request.args.get('clientId', current_app.config['BATTERY_CONFIG']['DEFAULT_CLIENT_ID'])
    
    # 导入数据模拟器
    try:
        from utils.data_simulator import BatteryDataSimulator
        simulator = BatteryDataSimulator()
        realtime_data = simulator.generate_realtime_data()
    except ImportError:
        # 如果模拟器还未创建，使用静态数据
        realtime_data = get_static_realtime_data()
    
    return jsonify({
        'code': 200,
        'message': '返回信息格式非json格式',
        'retain': None,
        'data': None,
        'detail': json.dumps(realtime_data, ensure_ascii=False),
        'dataObj': None
    })

def get_static_realtime_data():
    """获取静态实时数据（用于模拟器未创建时）"""
    return {
        'mobile': current_app.config['BATTERY_CONFIG']['DEFAULT_CLIENT_ID'],
        'heartBeatTime': '250618143708',
        'latitude': '22.506839',
        'longitude': '113.417564',
        'altitude': '3',
        'speed': '0',
        'direction': '164',
        'time': '250618143708',
        'batteryId': current_app.config['BATTERY_CONFIG']['DEFAULT_BATTERY_ID'],
        'batteryId26': current_app.config['BATTERY_CONFIG']['DEFAULT_BATTERY_ID'],
        'bmsVersion': '22',
        'batteryPackageInfo': json.dumps({
            'infoTime': '250618142903',
            'batteryId': current_app.config['BATTERY_CONFIG']['DEFAULT_BATTERY_ID'],
            'totalVoltage': '84.40',
            'totalCurrent': '0.00',
            'soc': '96',
            'cellQuantity': '20',
            'cellVoltageDetail': ['4.222', '4.223', '4.223', '4.224', '4.223', 
                                '4.221', '4.224', '4.225', '4.223', '4.225',
                                '4.220', '4.223', '4.224', '4.222', '4.223',
                                '4.224', '4.223', '4.223', '4.223', '4.222'],
            'tempQuantity': '2',
            'tempDetailInfo': ['34.00', '33.00'],
            'BMSTemp': '35',
            'residualCapacity': '51.30',
            'currentCapacity': '53.00',
            'batteryStatus': {
                'chargeMOSStatus': '1',
                'disChargeMOSStatus': '1',
                'DCMOS': '1',
                'chargeMOS': '1'
            }
        }, ensure_ascii=False)
    }

@battery_data_bp.route('/device-parameters', methods=['GET'])
def get_device_parameters():
    """获取设备参数 - 对应 /jeecg-boot/fnjbattery/deviceParameters"""
    
    client_id = request.args.get('clientId', current_app.config['BATTERY_CONFIG']['DEFAULT_CLIENT_ID'])
    key = request.args.get('key', current_app.config['BATTERY_CONFIG']['DEFAULT_KEY'])
    
    parameters = {
        'chargeCurrentCalibrationKValue': '1000',
        'chargingOverCurrentProtectionValue': '45',
        'dischargeOverCurrent1ProtectionDelay': '8',
        'overCurrentProtectionRecoveryTimeDelay': '300',
        'rechargeOverCurrentAlarmRecoveryValue': '20',
        'dischargeOverCurrent2ProtectionValue': '77',
        'dischargeOverCurrent2ProtectionDelay': '1280',
        'shortCircuitProtectionDelay': '406',
        'zeroCurrentCalibrationBValue': '3.0',
        'dischargeCurrentCalibrationKValue': '1001',
        'chargingOverCurrentAlarmValue': '40',
        'chargeOverCurrentProtectionDelay': '8',
        'dischargeOverCurrentAlarmValue': '55',
        'dischargeOverCurrentAlarmRecoveryValue': '40',
        'dischargeOverCurrentRecoveryDelay': '60',
        'shortCircuitProtectionCurrent': '500',
        'dischargeOverCurrent1ProtectionValue': '66'
    }
    
    return jsonify({
        'code': 200,
        'message': '返回信息格式非json格式',
        'retain': None,
        'data': None,
        'detail': json.dumps(parameters, ensure_ascii=False),
        'dataObj': None
    })

@battery_data_bp.route('/protection-warning', methods=['GET'])
def parse_protection_warning():
    """解析保护和警告信息 - 对应 /jeecg-boot/fnjbattery/parseProtectionAndWarning"""
    
    bms_status_info = request.args.get('bmsStatusInfo', '{}')
    
    # 解析BMS状态信息
    try:
        import urllib.parse
        decoded_info = urllib.parse.unquote(bms_status_info)
        bms_data = json.loads(decoded_info)
    except:
        bms_data = {}
    
    # 模拟解析结果
    alerts = []
    protections = []
    
    # 根据MOS状态生成警告信息
    charge_mos = bms_data.get('chargeMosStatus', 1)
    discharge_mos = bms_data.get('disChargeMosStatus', 1)
    
    if charge_mos == 1:
        alerts.append('充电MOS正常闭合')
    if discharge_mos == 1:
        alerts.append('放电MOS正常闭合')
    
    return jsonify({
        'success': True,
        'message': '',
        'code': 200,
        'result': {
            'alerts': alerts,
            'protections': protections
        },
        'timestamp': int(time.time() * 1000)
    })

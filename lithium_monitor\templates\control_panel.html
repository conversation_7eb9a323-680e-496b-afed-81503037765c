{% extends "base.html" %}

{% block title %}MOS控制面板 - 锂电监控系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0">
                    <i class="fas fa-sliders-h me-2"></i>MOS控制面板
                </h2>
                <div>
                    <span class="badge bg-success" id="connectionStatus">
                        <i class="fas fa-wifi me-1"></i>已连接
                    </span>
                    <span class="badge bg-info ms-2" id="lastControlTime">
                        最后控制: --
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- 当前状态卡片 -->
    <div class="row mb-4">
        <div class="col-md-6 mb-3">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-battery-full me-2"></i>充电MOS状态
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="mos-status-display mb-3" id="chargeMosDisplay">
                        <div class="mos-icon" id="chargeMosIcon">
                            <i class="fas fa-power-off fa-3x"></i>
                        </div>
                        <div class="mos-status-text mt-2">
                            <h4 id="chargeMosStatusText">开启</h4>
                            <small class="text-muted">允许充电</small>
                        </div>
                    </div>
                    <div class="mos-controls">
                        <button class="btn btn-success me-2" id="openChargeMos" onclick="controlMos('charge', 'open')">
                            <i class="fas fa-play me-1"></i>开启
                        </button>
                        <button class="btn btn-danger" id="closeChargeMos" onclick="controlMos('charge', 'close')">
                            <i class="fas fa-stop me-1"></i>关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-3">
            <div class="card h-100">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>放电MOS状态
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="mos-status-display mb-3" id="dischargeMosDisplay">
                        <div class="mos-icon" id="dischargeMosIcon">
                            <i class="fas fa-power-off fa-3x"></i>
                        </div>
                        <div class="mos-status-text mt-2">
                            <h4 id="dischargeMosStatusText">开启</h4>
                            <small class="text-muted">允许放电</small>
                        </div>
                    </div>
                    <div class="mos-controls">
                        <button class="btn btn-success me-2" id="openDischargeMos" onclick="controlMos('discharge', 'open')">
                            <i class="fas fa-play me-1"></i>开启
                        </button>
                        <button class="btn btn-danger" id="closeDischargeMos" onclick="controlMos('discharge', 'close')">
                            <i class="fas fa-stop me-1"></i>关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速控制面板 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tachometer-alt me-2"></i>快速控制
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-6 mb-3">
                            <button class="btn btn-success btn-lg w-100" onclick="controlMos('both', 'open')">
                                <i class="fas fa-play-circle me-2"></i>
                                <div>全部开启</div>
                                <small class="d-block">充电+放电</small>
                            </button>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <button class="btn btn-danger btn-lg w-100" onclick="controlMos('both', 'close')">
                                <i class="fas fa-stop-circle me-2"></i>
                                <div>全部关闭</div>
                                <small class="d-block">充电+放电</small>
                            </button>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <button class="btn btn-info btn-lg w-100" onclick="refreshStatus()">
                                <i class="fas fa-sync-alt me-2"></i>
                                <div>刷新状态</div>
                                <small class="d-block">获取最新状态</small>
                            </button>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <button class="btn btn-secondary btn-lg w-100" onclick="showControlHistory()">
                                <i class="fas fa-history me-2"></i>
                                <div>控制历史</div>
                                <small class="d-block">查看操作记录</small>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 控制历史记录 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>控制历史记录
                    </h5>
                    <button class="btn btn-outline-secondary btn-sm" onclick="clearHistory()">
                        <i class="fas fa-trash me-1"></i>清空记录
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>操作类型</th>
                                    <th>命令ID</th>
                                    <th>描述</th>
                                    <th>状态</th>
                                    <th>响应时间</th>
                                </tr>
                            </thead>
                            <tbody id="controlHistoryTable">
                                <tr>
                                    <td colspan="6" class="text-center text-muted">
                                        <i class="fas fa-info-circle me-2"></i>暂无控制记录
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统状态信息 -->
    <div class="row">
        <div class="col-md-6 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>设备信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">设备ID</small>
                            <div class="fw-bold" id="deviceIdInfo">--</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">BMS版本</small>
                            <div class="fw-bold" id="bmsVersionInfo">--</div>
                        </div>
                        <div class="col-6 mt-2">
                            <small class="text-muted">心跳时间</small>
                            <div class="fw-bold" id="heartBeatInfo">--</div>
                        </div>
                        <div class="col-6 mt-2">
                            <small class="text-muted">在线状态</small>
                            <div class="fw-bold text-success" id="onlineStatusInfo">
                                <i class="fas fa-circle me-1"></i>在线
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>安全状态
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">警告状态</small>
                            <div class="fw-bold text-success" id="alertStatusInfo">正常</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">保护状态</small>
                            <div class="fw-bold text-success" id="protectStatusInfo">正常</div>
                        </div>
                        <div class="col-6 mt-2">
                            <small class="text-muted">总电压</small>
                            <div class="fw-bold" id="voltageInfo">--</div>
                        </div>
                        <div class="col-6 mt-2">
                            <small class="text-muted">总电流</small>
                            <div class="fw-bold" id="currentInfo">--</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 确认对话框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmButton">确认</button>
            </div>
        </div>
    </div>
</div>

<!-- 控制历史详情模态框 -->
<div class="modal fade" id="historyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">控制历史详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="historyDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/control_panel.js') }}"></script>
{% endblock %}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import base64
import json
import urllib.parse
import xml.etree.ElementTree as ET
from typing import Dict, List, Any
import re

class RealDataAnalyzer:
    """真实数据分析器 - 分析XML中的所有真实数据结构"""
    
    def __init__(self, xml_file_path: str):
        self.xml_file_path = xml_file_path
        self.parsed_data = {}
        self.api_endpoints = {}
        
    def analyze_xml(self):
        """分析XML文件"""
        print("🔍 开始分析锂电.xml中的真实数据结构...")
        
        try:
            tree = ET.parse(self.xml_file_path)
            root = tree.getroot()
            
            # 分析所有item
            items = root.findall('.//item')
            print(f"📊 找到 {len(items)} 个API请求记录")
            
            for i, item in enumerate(items):
                self.analyze_item(item, i)
                
            self.generate_summary()
            
        except Exception as e:
            print(f"❌ 分析XML文件失败: {e}")
    
    def analyze_item(self, item, index):
        """分析单个API请求项"""
        try:
            # 提取基本信息
            url = item.find('url').text if item.find('url') is not None else ""
            method = item.find('method').text if item.find('method') is not None else ""
            
            # 提取路径
            path = self.extract_path_from_url(url)
            
            # 提取响应数据
            response_element = item.find('response')
            if response_element is not None:
                response_data = response_element.text
                decoded_response = self.decode_response(response_data)
                
                if decoded_response:
                    self.api_endpoints[path] = {
                        'method': method,
                        'url': url,
                        'response_structure': decoded_response,
                        'index': index
                    }
                    
                    print(f"✅ 解析API: {method} {path}")
                    
        except Exception as e:
            print(f"⚠️ 解析第{index}个项目失败: {e}")
    
    def extract_path_from_url(self, url: str) -> str:
        """从URL中提取路径"""
        if not url:
            return ""
        
        # 移除域名部分
        if 'jeecg-boot' in url:
            parts = url.split('jeecg-boot')
            if len(parts) > 1:
                path = parts[1].split('?')[0]  # 移除查询参数
                return path
        
        return url
    
    def decode_response(self, response_data: str) -> Dict:
        """解码响应数据"""
        try:
            # Base64解码
            decoded = base64.b64decode(response_data).decode('utf-8')
            
            # 分离HTTP头和JSON数据
            parts = decoded.split('\r\n\r\n', 1)
            if len(parts) == 1:
                parts = decoded.split('\n\n', 1)
            
            if len(parts) > 1:
                json_part = parts[1]
                return json.loads(json_part)
            
        except Exception as e:
            print(f"⚠️ 解码响应失败: {e}")
        
        return {}
    
    def generate_summary(self):
        """生成分析总结"""
        print("\n" + "="*80)
        print("📋 真实数据结构分析报告")
        print("="*80)
        
        # 按功能分类API
        data_apis = []
        control_apis = []
        config_apis = []
        
        for path, info in self.api_endpoints.items():
            if any(keyword in path for keyword in ['realTime', 'parseProtection', 'parseAlarm', 'list', 'deviceParameters']):
                data_apis.append((path, info))
            elif 'terminalControl' in path:
                control_apis.append((path, info))
            else:
                config_apis.append((path, info))
        
        print(f"\n📊 数据获取API ({len(data_apis)}个):")
        for path, info in data_apis:
            self.print_api_details(path, info)
        
        print(f"\n🎛️ 控制API ({len(control_apis)}个):")
        for path, info in control_apis:
            self.print_api_details(path, info)
        
        print(f"\n⚙️ 配置API ({len(config_apis)}个):")
        for path, info in config_apis:
            self.print_api_details(path, info)
        
        # 分析关键数据结构
        self.analyze_key_data_structures()
    
    def print_api_details(self, path: str, info: Dict):
        """打印API详细信息"""
        print(f"\n  🔗 {info['method']} {path}")
        
        response = info['response_structure']
        if response:
            print(f"     状态: {response.get('success', 'N/A')}")
            print(f"     代码: {response.get('code', 'N/A')}")
            
            # 分析result结构
            if 'result' in response:
                result = response['result']
                if isinstance(result, dict):
                    print(f"     结果字段: {list(result.keys())}")
                elif isinstance(result, list) and result:
                    print(f"     结果类型: 列表 (长度: {len(result)})")
                    if isinstance(result[0], dict):
                        print(f"     列表项字段: {list(result[0].keys())}")
            
            # 分析detail字段（重要的嵌套JSON）
            if 'detail' in response:
                detail = response['detail']
                if isinstance(detail, str):
                    try:
                        detail_obj = json.loads(detail)
                        print(f"     详细信息字段: {list(detail_obj.keys())}")
                    except:
                        print(f"     详细信息: 字符串格式")
    
    def analyze_key_data_structures(self):
        """分析关键数据结构"""
        print(f"\n🔍 关键数据结构分析:")
        
        # 分析实时数据结构
        realtime_path = None
        for path in self.api_endpoints:
            if 'realTime' in path:
                realtime_path = path
                break
        
        if realtime_path:
            self.analyze_realtime_structure(realtime_path)
        
        # 分析控制命令结构
        control_paths = [path for path in self.api_endpoints if 'terminalControl' in path]
        if control_paths:
            self.analyze_control_structure(control_paths)
    
    def analyze_realtime_structure(self, path: str):
        """分析实时数据结构"""
        print(f"\n  📊 实时数据结构 ({path}):")
        
        response = self.api_endpoints[path]['response_structure']
        if 'detail' in response:
            try:
                detail_str = response['detail']
                detail_obj = json.loads(detail_str)
                
                print(f"     主要字段:")
                for key, value in detail_obj.items():
                    if key == 'batteryPackageInfo':
                        battery_info = json.loads(value)
                        print(f"       {key}: 电池包信息 (包含{len(battery_info)}个字段)")
                        print(f"         核心字段: totalVoltage, totalCurrent, soc, cellQuantity")
                    elif key == 'bmsStatusInfo':
                        bms_info = json.loads(value)
                        print(f"       {key}: BMS状态信息 (包含{len(bms_info)}个字段)")
                    else:
                        print(f"       {key}: {type(value).__name__}")
                        
            except Exception as e:
                print(f"     ⚠️ 解析实时数据失败: {e}")
    
    def analyze_control_structure(self, paths: List[str]):
        """分析控制命令结构"""
        print(f"\n  🎛️ 控制命令结构:")
        
        cmd_ids = set()
        for path in paths:
            # 从URL中提取cmdId
            url = self.api_endpoints[path]['url']
            if 'cmdId=' in url:
                cmd_id = url.split('cmdId=')[1].split('&')[0]
                cmd_ids.add(cmd_id)
        
        print(f"     发现的控制命令: {sorted(cmd_ids)}")
        
        # 分析响应结构
        if paths:
            response = self.api_endpoints[paths[0]]['response_structure']
            if 'result' in response:
                result = response['result']
                if isinstance(result, dict):
                    print(f"     响应字段: {list(result.keys())}")
    
    def export_real_data_config(self):
        """导出真实数据配置"""
        config = {
            'api_endpoints': self.api_endpoints,
            'data_structures': self.extract_data_structures()
        }
        
        with open('real_data_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 真实数据配置已导出到: real_data_config.json")
    
    def extract_data_structures(self) -> Dict:
        """提取数据结构定义"""
        structures = {}
        
        for path, info in self.api_endpoints.items():
            response = info['response_structure']
            
            # 提取实时数据结构
            if 'realTime' in path and 'detail' in response:
                try:
                    detail_obj = json.loads(response['detail'])
                    structures['realtime_data'] = self.extract_structure_schema(detail_obj)
                    
                    # 提取电池包信息结构
                    if 'batteryPackageInfo' in detail_obj:
                        battery_info = json.loads(detail_obj['batteryPackageInfo'])
                        structures['battery_package_info'] = self.extract_structure_schema(battery_info)
                        
                except:
                    pass
            
            # 提取电池型号结构
            elif 'wyBatteryInfor/list' in path and 'result' in response:
                result = response['result']
                if 'records' in result and result['records']:
                    structures['battery_model'] = self.extract_structure_schema(result['records'][0])
        
        return structures
    
    def extract_structure_schema(self, obj: Dict) -> Dict:
        """提取对象结构模式"""
        schema = {}
        for key, value in obj.items():
            if isinstance(value, str):
                schema[key] = 'string'
            elif isinstance(value, int):
                schema[key] = 'integer'
            elif isinstance(value, float):
                schema[key] = 'float'
            elif isinstance(value, bool):
                schema[key] = 'boolean'
            elif isinstance(value, list):
                schema[key] = 'array'
            elif isinstance(value, dict):
                schema[key] = 'object'
            else:
                schema[key] = 'unknown'
        
        return schema

if __name__ == "__main__":
    analyzer = RealDataAnalyzer("锂电.xml")
    analyzer.analyze_xml()
    analyzer.export_real_data_config()

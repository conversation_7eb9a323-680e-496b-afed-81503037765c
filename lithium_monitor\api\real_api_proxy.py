#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
from flask import Blueprint, request, jsonify
from typing import Dict, Any

# 创建真实API代理蓝图
real_api_proxy_bp = Blueprint('real_api_proxy', __name__)

# 万洋锂电服务器配置
WANYANG_SERVER = "https://sys.wyzxcn.com/jeecg-boot"
DEFAULT_AUTH = {
    'key': '79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c',
    'clientId': '380074209785'
}

# 请求超时配置
REQUEST_TIMEOUT = 10
RETRY_COUNT = 3

class RealAPIProxy:
    """万洋锂电真实API代理"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache'
        })
        
    def make_request(self, endpoint: str, params: Dict[str, Any] = None, method: str = 'GET') -> Dict[str, Any]:
        """发送请求到万洋锂电服务器"""
        url = f"{WANYANG_SERVER}{endpoint}"
        
        # 合并认证参数
        if params is None:
            params = {}
        params.update(DEFAULT_AUTH)
        
        for attempt in range(RETRY_COUNT):
            try:
                print(f"🔗 请求万洋锂电服务器: {url}")
                print(f"📋 参数: {params}")
                
                if method.upper() == 'GET':
                    response = self.session.get(url, params=params, timeout=REQUEST_TIMEOUT)
                else:
                    response = self.session.post(url, data=params, timeout=REQUEST_TIMEOUT)
                
                print(f"📊 响应状态: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"✅ 获取真实数据成功")
                        return {
                            'success': True,
                            'data': data,
                            'timestamp': int(time.time() * 1000)
                        }
                    except json.JSONDecodeError:
                        # 如果不是JSON，返回文本内容
                        return {
                            'success': True,
                            'data': response.text,
                            'timestamp': int(time.time() * 1000)
                        }
                else:
                    print(f"❌ 服务器响应错误: {response.status_code}")
                    
            except requests.exceptions.Timeout:
                print(f"⏰ 请求超时 (尝试 {attempt + 1}/{RETRY_COUNT})")
                if attempt == RETRY_COUNT - 1:
                    return {
                        'success': False,
                        'error': '请求超时',
                        'timestamp': int(time.time() * 1000)
                    }
                    
            except requests.exceptions.ConnectionError:
                print(f"🔌 连接错误 (尝试 {attempt + 1}/{RETRY_COUNT})")
                if attempt == RETRY_COUNT - 1:
                    return {
                        'success': False,
                        'error': '连接服务器失败',
                        'timestamp': int(time.time() * 1000)
                    }
                    
            except Exception as e:
                print(f"❌ 请求异常: {e}")
                return {
                    'success': False,
                    'error': str(e),
                    'timestamp': int(time.time() * 1000)
                }
        
        return {
            'success': False,
            'error': '请求失败',
            'timestamp': int(time.time() * 1000)
        }

# 创建代理实例
proxy = RealAPIProxy()

@real_api_proxy_bp.route('/realtime', methods=['GET'])
def get_real_realtime_data():
    """获取真实的实时电池数据"""
    print("\n🔄 代理请求: 获取真实实时数据")
    
    # 从万洋锂电服务器获取真实数据
    result = proxy.make_request('/fnjbattery/realTime')
    
    if result['success']:
        # 返回真实服务器的数据格式
        return jsonify(result['data'])
    else:
        return jsonify({
            'code': 500,
            'message': f'获取真实数据失败: {result["error"]}',
            'success': False,
            'timestamp': result['timestamp']
        }), 500

@real_api_proxy_bp.route('/control', methods=['GET'])
def send_real_control_command():
    """发送真实的控制命令"""
    print("\n🎛️ 代理请求: 发送真实控制命令")
    
    cmd_id = request.args.get('cmdId')
    if not cmd_id:
        return jsonify({
            'success': False,
            'message': '缺少控制命令ID',
            'code': 400
        }), 400
    
    print(f"📤 发送控制命令: {cmd_id}")
    
    # 发送控制命令到万洋锂电服务器
    params = {'cmdId': cmd_id}
    result = proxy.make_request('/fnjbattery/terminalControl', params)
    
    if result['success']:
        print(f"✅ 控制命令执行成功")
        return jsonify(result['data'])
    else:
        print(f"❌ 控制命令执行失败: {result['error']}")
        return jsonify({
            'code': 500,
            'message': f'控制命令执行失败: {result["error"]}',
            'success': False,
            'timestamp': result['timestamp']
        }), 500

@real_api_proxy_bp.route('/battery-models', methods=['GET'])
def get_real_battery_models():
    """获取真实的电池型号列表"""
    print("\n📋 代理请求: 获取真实电池型号")
    
    # 从万洋锂电服务器获取电池型号
    result = proxy.make_request('/wybattery/wyBatteryInfor/list')
    
    if result['success']:
        return jsonify(result['data'])
    else:
        return jsonify({
            'code': 500,
            'message': f'获取电池型号失败: {result["error"]}',
            'success': False,
            'timestamp': result['timestamp']
        }), 500

@real_api_proxy_bp.route('/user-batteries', methods=['GET'])
def get_real_user_batteries():
    """获取真实的用户电池列表"""
    print("\n👤 代理请求: 获取真实用户电池")
    
    user_id = request.args.get('userId', '1933343591078334465')
    
    # 从万洋锂电服务器获取用户电池
    params = {'userId': user_id}
    result = proxy.make_request('/battery/userBattery/api/list', params)
    
    if result['success']:
        return jsonify(result['data'])
    else:
        return jsonify({
            'code': 500,
            'message': f'获取用户电池失败: {result["error"]}',
            'success': False,
            'timestamp': result['timestamp']
        }), 500

@real_api_proxy_bp.route('/parse-protection', methods=['GET'])
def parse_real_protection():
    """解析真实的保护和警告信息"""
    print("\n🛡️ 代理请求: 解析真实保护信息")
    
    bms_status_info = request.args.get('bmsStatusInfo')
    if not bms_status_info:
        return jsonify({
            'success': False,
            'message': '缺少BMS状态信息',
            'code': 400
        }), 400
    
    # 发送到万洋锂电服务器解析
    params = {'bmsStatusInfo': bms_status_info}
    result = proxy.make_request('/fnjbattery/parseProtectionAndWarning', params)
    
    if result['success']:
        return jsonify(result['data'])
    else:
        return jsonify({
            'code': 500,
            'message': f'解析保护信息失败: {result["error"]}',
            'success': False,
            'timestamp': result['timestamp']
        }), 500

@real_api_proxy_bp.route('/parse-alarm', methods=['GET'])
def parse_real_alarm():
    """解析真实的警报和电池信息"""
    print("\n⚠️ 代理请求: 解析真实警报信息")
    
    battery_package_info = request.args.get('batteryPackageInfo')
    bms_status_info = request.args.get('bmsStatusInfo')
    
    if not battery_package_info:
        return jsonify({
            'success': False,
            'message': '缺少电池包信息',
            'code': 400
        }), 400
    
    # 发送到万洋锂电服务器解析
    params = {'batteryPackageInfo': battery_package_info}
    if bms_status_info:
        params['bmsStatusInfo'] = bms_status_info
        
    result = proxy.make_request('/fnjbattery/parseAlarmAndBattery', params)
    
    if result['success']:
        return jsonify(result['data'])
    else:
        return jsonify({
            'code': 500,
            'message': f'解析警报信息失败: {result["error"]}',
            'success': False,
            'timestamp': result['timestamp']
        }), 500

@real_api_proxy_bp.route('/test-connection', methods=['GET'])
def test_real_connection():
    """测试与万洋锂电服务器的连接"""
    print("\n🔗 测试与万洋锂电服务器连接...")
    
    # 测试实时数据接口
    result = proxy.make_request('/fnjbattery/realTime')
    
    if result['success']:
        return jsonify({
            'success': True,
            'message': '连接万洋锂电服务器成功',
            'server': WANYANG_SERVER,
            'timestamp': result['timestamp']
        })
    else:
        return jsonify({
            'success': False,
            'message': f'连接万洋锂电服务器失败: {result["error"]}',
            'server': WANYANG_SERVER,
            'timestamp': result['timestamp']
        }), 500

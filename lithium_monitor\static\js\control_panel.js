// MOS控制面板JavaScript

// 全局变量
let controlHistory = JSON.parse(localStorage.getItem('controlHistory') || '[]');
let currentMosStatus = { charge: 1, discharge: 1 };
let pendingControl = null;

// 页面加载完成后初始化
$(document).ready(function() {
    console.log('控制面板初始化...');
    
    // 初始化界面
    initControlPanel();
    
    // 获取初始状态
    refreshStatus();
    
    // 设置定时刷新
    setInterval(refreshStatus, 10000); // 每10秒刷新一次状态
    
    // 加载控制历史
    loadControlHistory();
});

function initControlPanel() {
    console.log('初始化控制面板界面...');
    
    // 设置确认按钮事件
    $('#confirmButton').click(function() {
        if (pendingControl) {
            executeControl(pendingControl);
            $('#confirmModal').modal('hide');
        }
    });
    
    // 添加键盘快捷键
    $(document).keydown(function(e) {
        if (e.ctrlKey) {
            switch(e.key) {
                case '1':
                    e.preventDefault();
                    controlMos('both', 'open');
                    break;
                case '2':
                    e.preventDefault();
                    controlMos('both', 'close');
                    break;
                case 'r':
                    e.preventDefault();
                    refreshStatus();
                    break;
            }
        }
    });
}

function controlMos(type, action) {
    console.log(`控制MOS: ${type} - ${action}`);
    
    let cmdId, description, confirmMessage;
    
    // 确定控制命令
    switch(`${type}_${action}`) {
        case 'charge_open':
            // 充电MOS单独控制需要组合命令
            cmdId = currentMosStatus.discharge === 1 ? '08' : '41'; // 如果放电MOS开启，用08；否则只开充电
            description = '开启充电MOS';
            confirmMessage = '确定要开启充电MOS吗？这将允许电池充电。';
            break;
        case 'charge_close':
            cmdId = currentMosStatus.discharge === 1 ? '40' : '09'; // 如果放电MOS开启，只关充电；否则全关
            description = '关闭充电MOS';
            confirmMessage = '确定要关闭充电MOS吗？这将禁止电池充电。';
            break;
        case 'discharge_open':
            cmdId = currentMosStatus.charge === 1 ? '08' : '41'; // 如果充电MOS开启，用08；否则只开放电
            description = '开启放电MOS';
            confirmMessage = '确定要开启放电MOS吗？这将允许电池放电。';
            break;
        case 'discharge_close':
            cmdId = '40'; // 关闭放电MOS
            description = '关闭放电MOS';
            confirmMessage = '确定要关闭放电MOS吗？这将禁止电池放电。';
            break;
        case 'both_open':
            cmdId = '08'; // 打开充/放电MOS（同时）
            description = '开启充电和放电MOS';
            confirmMessage = '确定要同时开启充电和放电MOS吗？这将允许电池正常充放电。';
            break;
        case 'both_close':
            cmdId = '09'; // 关闭充/放电MOS（同时）
            description = '关闭充电和放电MOS';
            confirmMessage = '确定要同时关闭充电和放电MOS吗？这将完全断开电池连接。';
            break;
        default:
            showNotification('未知的控制命令', 'error');
            return;
    }
    
    // 设置待执行的控制命令
    pendingControl = {
        cmdId: cmdId,
        type: type,
        action: action,
        description: description,
        timestamp: new Date()
    };
    
    // 显示确认对话框
    $('#confirmMessage').text(confirmMessage);
    $('#confirmModal').modal('show');
}

function executeControl(control) {
    console.log('执行控制命令:', control);
    
    // 显示加载状态
    showControlLoading(control.type, control.action);
    
    const startTime = Date.now();
    
    // 发送控制命令
    $.ajax({
        url: '/api/control/terminal',
        method: 'GET',
        data: {
            key: APP_CONFIG.realData.batteryConfig.defaultKey,
            clientId: APP_CONFIG.realData.batteryConfig.defaultClientId,
            cmdId: control.cmdId
        },
        timeout: 10000,
        success: function(response) {
            const responseTime = Date.now() - startTime;
            
            if (response.success) {
                console.log('控制命令执行成功:', response);
                
                // 更新控制历史
                addControlHistory({
                    ...control,
                    status: 'success',
                    responseTime: responseTime,
                    response: response
                });
                
                // 显示成功通知
                showNotification(`${control.description}成功`, 'success');
                
                // 更新状态显示
                setTimeout(refreshStatus, 1000); // 1秒后刷新状态
                
            } else {
                console.error('控制命令执行失败:', response);
                handleControlError(control, response.message || '控制命令执行失败', responseTime);
            }
        },
        error: function(xhr, status, error) {
            const responseTime = Date.now() - startTime;
            console.error('控制命令请求失败:', error);
            handleControlError(control, `网络错误: ${error}`, responseTime);
        },
        complete: function() {
            // 隐藏加载状态
            hideControlLoading();
        }
    });
}

function handleControlError(control, errorMessage, responseTime) {
    // 更新控制历史
    addControlHistory({
        ...control,
        status: 'error',
        responseTime: responseTime,
        error: errorMessage
    });
    
    // 显示错误通知
    showNotification(`${control.description}失败: ${errorMessage}`, 'error');
}

function showControlLoading(type, action) {
    // 禁用相关按钮并显示加载状态
    if (type === 'charge' || type === 'both') {
        $('#openChargeMos, #closeChargeMos').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>执行中...');
    }
    if (type === 'discharge' || type === 'both') {
        $('#openDischargeMos, #closeDischargeMos').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>执行中...');
    }
}

function hideControlLoading() {
    // 恢复按钮状态
    $('#openChargeMos').prop('disabled', false).html('<i class="fas fa-play me-1"></i>开启');
    $('#closeChargeMos').prop('disabled', false).html('<i class="fas fa-stop me-1"></i>关闭');
    $('#openDischargeMos').prop('disabled', false).html('<i class="fas fa-play me-1"></i>开启');
    $('#closeDischargeMos').prop('disabled', false).html('<i class="fas fa-stop me-1"></i>关闭');
}

function refreshStatus() {
    console.log('刷新MOS状态...');
    
    // 获取实时数据
    $.ajax({
        url: '/api/battery/realtime',
        method: 'GET',
        data: {
            key: APP_CONFIG.realData.batteryConfig.defaultKey,
            clientId: APP_CONFIG.realData.batteryConfig.defaultClientId
        },
        success: function(response) {
            try {
                const data = JSON.parse(response.detail);
                const batteryInfo = JSON.parse(data.batteryPackageInfo);
                const bmsInfo = JSON.parse(data.bmsStatusInfo);
                
                // 更新MOS状态
                updateMosStatus(batteryInfo.batteryStatus, bmsInfo);
                
                // 更新设备信息
                updateDeviceInfo(data, bmsInfo);
                
                // 更新安全状态
                updateSafetyStatus(batteryInfo, bmsInfo);
                
                // 更新连接状态
                $('#connectionStatus').removeClass('bg-danger').addClass('bg-success')
                    .html('<i class="fas fa-wifi me-1"></i>已连接');
                
            } catch (error) {
                console.error('解析状态数据失败:', error);
                showNotification('状态数据解析失败', 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('获取状态失败:', error);
            $('#connectionStatus').removeClass('bg-success').addClass('bg-danger')
                .html('<i class="fas fa-wifi-slash me-1"></i>连接失败');
            showNotification('获取设备状态失败', 'error');
        }
    });
}

function updateMosStatus(batteryStatus, bmsInfo) {
    const chargeMos = parseInt(batteryStatus.chargeMOSStatus);
    const dischargeMos = parseInt(batteryStatus.disChargeMOSStatus);
    
    // 更新全局状态
    currentMosStatus = { charge: chargeMos, discharge: dischargeMos };
    
    // 更新充电MOS显示
    updateMosDisplay('charge', chargeMos);
    
    // 更新放电MOS显示
    updateMosDisplay('discharge', dischargeMos);
    
    console.log('MOS状态更新:', currentMosStatus);
}

function updateMosDisplay(type, status) {
    const isOn = status === 1;
    const prefix = type === 'charge' ? 'charge' : 'discharge';
    
    // 更新图标
    const icon = $(`#${prefix}MosIcon i`);
    icon.removeClass('text-success text-danger')
        .addClass(isOn ? 'text-success' : 'text-danger');
    
    // 更新状态文本
    $(`#${prefix}MosStatusText`).text(isOn ? '开启' : '关闭')
        .removeClass('text-success text-danger')
        .addClass(isOn ? 'text-success' : 'text-danger');
    
    // 更新显示区域背景
    $(`#${prefix}MosDisplay`).removeClass('bg-light bg-success bg-danger')
        .addClass(isOn ? 'bg-light' : 'bg-light');
}

function updateDeviceInfo(data, bmsInfo) {
    $('#deviceIdInfo').text(data.batteryId || '--');
    $('#bmsVersionInfo').text(data.bmsVersion || '--');
    $('#heartBeatInfo').text(formatHeartBeatTime(data.heartBeatTime) || '--');
}

function updateSafetyStatus(batteryInfo, bmsInfo) {
    // 更新警告状态
    const alertStatus = bmsInfo.alertStatusBit === '0000000000000000' ? '正常' : '警告';
    $('#alertStatusInfo').text(alertStatus)
        .removeClass('text-success text-warning text-danger')
        .addClass(alertStatus === '正常' ? 'text-success' : 'text-warning');
    
    // 更新保护状态
    const protectStatus = bmsInfo.protectStatusBit === '0000000000000000' ? '正常' : '保护';
    $('#protectStatusInfo').text(protectStatus)
        .removeClass('text-success text-warning text-danger')
        .addClass(protectStatus === '正常' ? 'text-success' : 'text-danger');
    
    // 更新电压电流
    $('#voltageInfo').text(`${batteryInfo.totalVoltage}V`);
    $('#currentInfo').text(`${batteryInfo.totalCurrent}A`);
}

function addControlHistory(record) {
    // 添加到历史记录
    controlHistory.unshift(record);
    
    // 限制历史记录数量
    if (controlHistory.length > 100) {
        controlHistory = controlHistory.slice(0, 100);
    }
    
    // 保存到本地存储
    localStorage.setItem('controlHistory', JSON.stringify(controlHistory));
    
    // 更新显示
    loadControlHistory();
    
    // 更新最后控制时间
    $('#lastControlTime').text(`最后控制: ${formatTime(record.timestamp)}`);
}

function loadControlHistory() {
    const tbody = $('#controlHistoryTable');
    tbody.empty();
    
    if (controlHistory.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="6" class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>暂无控制记录
                </td>
            </tr>
        `);
        return;
    }
    
    // 显示最近20条记录
    const recentHistory = controlHistory.slice(0, 20);
    
    recentHistory.forEach(record => {
        const statusBadge = record.status === 'success' 
            ? '<span class="badge bg-success">成功</span>'
            : '<span class="badge bg-danger">失败</span>';
        
        const row = `
            <tr>
                <td>${formatTime(record.timestamp)}</td>
                <td>${record.description}</td>
                <td><code>${record.cmdId}</code></td>
                <td>${record.description}</td>
                <td>${statusBadge}</td>
                <td>${record.responseTime}ms</td>
            </tr>
        `;
        tbody.append(row);
    });
}

function clearHistory() {
    if (confirm('确定要清空所有控制历史记录吗？')) {
        controlHistory = [];
        localStorage.removeItem('controlHistory');
        loadControlHistory();
        $('#lastControlTime').text('最后控制: --');
        showNotification('控制历史已清空', 'info');
    }
}

function showControlHistory() {
    $('#historyModal').modal('show');
}

function formatTime(timestamp) {
    return new Date(timestamp).toLocaleString('zh-CN');
}

function formatHeartBeatTime(timeStr) {
    if (!timeStr || timeStr.length !== 12) return '--';
    
    const year = '20' + timeStr.substr(0, 2);
    const month = timeStr.substr(2, 2);
    const day = timeStr.substr(4, 2);
    const hour = timeStr.substr(6, 2);
    const minute = timeStr.substr(8, 2);
    const second = timeStr.substr(10, 2);
    
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
}

function showNotification(message, type = 'info') {
    // 创建通知元素
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';
    
    const notification = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <strong>${message}</strong>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    // 添加到页面
    $('body').append(notification);
    
    // 3秒后自动消失
    setTimeout(() => {
        notification.alert('close');
    }, 3000);
}

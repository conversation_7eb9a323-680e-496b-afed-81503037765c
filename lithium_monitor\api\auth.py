#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import jwt
import time
from functools import wraps
from flask import Blueprint, request, jsonify, current_app

# 创建认证蓝图
auth_bp = Blueprint('auth', __name__)

def generate_token(user_id, client_id):
    """生成JWT Token"""
    payload = {
        'user_id': user_id,
        'client_id': client_id,
        'exp': int(time.time()) + current_app.config['JWT_ACCESS_TOKEN_EXPIRES'].total_seconds(),
        'iat': int(time.time())
    }
    
    token = jwt.encode(
        payload, 
        current_app.config['JWT_SECRET_KEY'], 
        algorithm='HS256'
    )
    
    return token

def verify_token(token):
    """验证JWT Token"""
    try:
        payload = jwt.decode(
            token, 
            current_app.config['JWT_SECRET_KEY'], 
            algorithms=['HS256']
        )
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

def require_auth(f):
    """认证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get('X-Access-Token')
        
        if not token:
            return jsonify({
                'success': False,
                'message': '缺少访问令牌',
                'code': 401
            }), 401
        
        payload = verify_token(token)
        if not payload:
            return jsonify({
                'success': False,
                'message': '无效的访问令牌',
                'code': 401
            }), 401
        
        # 将用户信息添加到请求上下文
        request.current_user = payload
        return f(*args, **kwargs)
    
    return decorated_function

@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    data = request.get_json()
    
    if not data:
        return jsonify({
            'success': False,
            'message': '请求数据格式错误',
            'code': 400
        }), 400
    
    username = data.get('username')
    password = data.get('password')
    
    # 简单的演示认证逻辑
    if username == 'admin' and password == 'admin123':
        user_id = current_app.config['BATTERY_CONFIG']['DEFAULT_USER_ID']
        client_id = current_app.config['BATTERY_CONFIG']['DEFAULT_CLIENT_ID']
        
        token = generate_token(user_id, client_id)
        
        return jsonify({
            'success': True,
            'message': '登录成功',
            'code': 200,
            'result': {
                'token': token,
                'user_id': user_id,
                'client_id': client_id,
                'username': username
            }
        })
    else:
        return jsonify({
            'success': False,
            'message': '用户名或密码错误',
            'code': 401
        }), 401

@auth_bp.route('/verify', methods=['POST'])
def verify():
    """验证Token"""
    token = request.headers.get('X-Access-Token')
    
    if not token:
        return jsonify({
            'success': False,
            'message': '缺少访问令牌',
            'code': 401
        }), 401
    
    payload = verify_token(token)
    if payload:
        return jsonify({
            'success': True,
            'message': '令牌有效',
            'code': 200,
            'result': payload
        })
    else:
        return jsonify({
            'success': False,
            'message': '令牌无效或已过期',
            'code': 401
        }), 401

@auth_bp.route('/refresh', methods=['POST'])
def refresh():
    """刷新Token"""
    token = request.headers.get('X-Access-Token')
    
    if not token:
        return jsonify({
            'success': False,
            'message': '缺少访问令牌',
            'code': 401
        }), 401
    
    payload = verify_token(token)
    if payload:
        # 生成新的Token
        new_token = generate_token(payload['user_id'], payload['client_id'])
        
        return jsonify({
            'success': True,
            'message': '令牌刷新成功',
            'code': 200,
            'result': {
                'token': new_token,
                'user_id': payload['user_id'],
                'client_id': payload['client_id']
            }
        })
    else:
        return jsonify({
            'success': False,
            'message': '令牌无效，无法刷新',
            'code': 401
        }), 401

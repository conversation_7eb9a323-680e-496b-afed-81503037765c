/**
 * 锂电监控系统 - 通用JavaScript函数
 */

// 全局工具函数
window.LithiumMonitor = {
    
    // API请求封装
    api: {
        request: function(url, options = {}) {
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                timeout: 30000
            };
            
            const config = Object.assign(defaultOptions, options);
            
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.open(config.method, url);
                
                // 设置请求头
                Object.keys(config.headers).forEach(key => {
                    xhr.setRequestHeader(key, config.headers[key]);
                });
                
                // 设置超时
                xhr.timeout = config.timeout;
                
                xhr.onload = function() {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (e) {
                            resolve(xhr.responseText);
                        }
                    } else {
                        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                    }
                };
                
                xhr.onerror = function() {
                    reject(new Error('网络连接错误'));
                };
                
                xhr.ontimeout = function() {
                    reject(new Error('请求超时'));
                };
                
                // 发送请求
                if (config.body) {
                    xhr.send(JSON.stringify(config.body));
                } else {
                    xhr.send();
                }
            });
        },
        
        get: function(url, params = {}) {
            const queryString = Object.keys(params)
                .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
                .join('&');
            
            const fullUrl = queryString ? `${url}?${queryString}` : url;
            return this.request(fullUrl);
        },
        
        post: function(url, data = {}) {
            return this.request(url, {
                method: 'POST',
                body: data
            });
        }
    },
    
    // 数据格式化工具
    format: {
        voltage: function(value) {
            return `${parseFloat(value).toFixed(2)}V`;
        },
        
        current: function(value) {
            const num = parseFloat(value);
            return `${num >= 0 ? '+' : ''}${num.toFixed(2)}A`;
        },
        
        temperature: function(value) {
            return `${parseFloat(value).toFixed(1)}°C`;
        },
        
        percentage: function(value) {
            return `${parseInt(value)}%`;
        },
        
        capacity: function(value) {
            const num = parseFloat(value);
            return num >= 1000 ? `${(num/1000).toFixed(1)}kAh` : `${num.toFixed(1)}Ah`;
        },
        
        datetime: function(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN');
        },
        
        time: function(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleTimeString('zh-CN');
        }
    },
    
    // 数据验证工具
    validate: {
        voltage: function(value) {
            const num = parseFloat(value);
            return !isNaN(num) && num >= 0 && num <= 100;
        },
        
        current: function(value) {
            const num = parseFloat(value);
            return !isNaN(num) && num >= -200 && num <= 200;
        },
        
        temperature: function(value) {
            const num = parseFloat(value);
            return !isNaN(num) && num >= -40 && num <= 80;
        },
        
        soc: function(value) {
            const num = parseInt(value);
            return !isNaN(num) && num >= 0 && num <= 100;
        },
        
        coordinates: function(lat, lon) {
            const latitude = parseFloat(lat);
            const longitude = parseFloat(lon);
            return !isNaN(latitude) && !isNaN(longitude) && 
                   latitude >= -90 && latitude <= 90 &&
                   longitude >= -180 && longitude <= 180;
        }
    },
    
    // 状态管理
    status: {
        getMosStatusText: function(status) {
            return status === 1 || status === '1' ? '开启' : '关闭';
        },
        
        getMosStatusClass: function(status) {
            return status === 1 || status === '1' ? 'bg-success' : 'bg-danger';
        },
        
        getProtectionLevelText: function(level) {
            const levels = {
                0: '正常',
                1: '警告', 
                2: '保护',
                3: '严重'
            };
            return levels[level] || '未知';
        },
        
        getProtectionLevelClass: function(level) {
            const classes = {
                0: 'text-success',
                1: 'text-warning',
                2: 'text-danger', 
                3: 'text-danger'
            };
            return classes[level] || 'text-muted';
        }
    },
    
    // 图表工具
    chart: {
        defaultColors: {
            voltage: '#007bff',
            current: '#28a745', 
            temperature: '#dc3545',
            soc: '#ffc107',
            power: '#6f42c1'
        },
        
        createLineChart: function(ctx, config = {}) {
            const defaultConfig = {
                type: 'line',
                data: {
                    labels: [],
                    datasets: []
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '数值'
                            }
                        }
                    }
                }
            };
            
            const mergedConfig = this.mergeDeep(defaultConfig, config);
            return new Chart(ctx, mergedConfig);
        },
        
        createBarChart: function(ctx, config = {}) {
            const defaultConfig = {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: []
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            };
            
            const mergedConfig = this.mergeDeep(defaultConfig, config);
            return new Chart(ctx, mergedConfig);
        },
        
        mergeDeep: function(target, source) {
            const output = Object.assign({}, target);
            if (this.isObject(target) && this.isObject(source)) {
                Object.keys(source).forEach(key => {
                    if (this.isObject(source[key])) {
                        if (!(key in target))
                            Object.assign(output, { [key]: source[key] });
                        else
                            output[key] = this.mergeDeep(target[key], source[key]);
                    } else {
                        Object.assign(output, { [key]: source[key] });
                    }
                });
            }
            return output;
        },
        
        isObject: function(item) {
            return item && typeof item === 'object' && !Array.isArray(item);
        }
    },
    
    // 本地存储工具
    storage: {
        set: function(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (e) {
                console.error('存储数据失败:', e);
                return false;
            }
        },
        
        get: function(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (e) {
                console.error('读取数据失败:', e);
                return defaultValue;
            }
        },
        
        remove: function(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (e) {
                console.error('删除数据失败:', e);
                return false;
            }
        },
        
        clear: function() {
            try {
                localStorage.clear();
                return true;
            } catch (e) {
                console.error('清空数据失败:', e);
                return false;
            }
        }
    },
    
    // 工具函数
    utils: {
        debounce: function(func, wait, immediate) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    timeout = null;
                    if (!immediate) func(...args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func(...args);
            };
        },
        
        throttle: function(func, limit) {
            let inThrottle;
            return function(...args) {
                if (!inThrottle) {
                    func.apply(this, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },
        
        generateId: function() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        },
        
        copyToClipboard: function(text) {
            if (navigator.clipboard) {
                return navigator.clipboard.writeText(text);
            } else {
                // 兼容旧浏览器
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    return Promise.resolve();
                } catch (err) {
                    document.body.removeChild(textArea);
                    return Promise.reject(err);
                }
            }
        },
        
        downloadFile: function(data, filename, type = 'text/plain') {
            const blob = new Blob([data], { type });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        }
    }
};

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('全局错误:', e.error);
});

window.addEventListener('unhandledrejection', function(e) {
    console.error('未处理的Promise拒绝:', e.reason);
});

// 导出到全局
window.LM = window.LithiumMonitor;

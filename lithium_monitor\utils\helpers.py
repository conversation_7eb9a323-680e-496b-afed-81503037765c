#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import base64

def get_current_timestamp() -> int:
    """获取当前时间戳（毫秒）"""
    return int(time.time() * 1000)

def format_timestamp(timestamp: float, format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
    """格式化时间戳"""
    return datetime.fromtimestamp(timestamp).strftime(format_str)

def generate_battery_id() -> str:
    """生成电池ID"""
    timestamp = str(int(time.time()))
    random_str = hashlib.md5(timestamp.encode()).hexdigest()[:8]
    return f"BT{timestamp[-10:]}{random_str.upper()}"

def generate_client_id() -> str:
    """生成客户端ID"""
    timestamp = str(int(time.time()))
    return timestamp[-12:]

def validate_voltage(voltage: float) -> bool:
    """验证电压值是否合理"""
    return 0.0 <= voltage <= 100.0

def validate_current(current: float) -> bool:
    """验证电流值是否合理"""
    return -200.0 <= current <= 200.0

def validate_temperature(temperature: float) -> bool:
    """验证温度值是否合理"""
    return -40.0 <= temperature <= 80.0

def validate_soc(soc: int) -> bool:
    """验证SOC值是否合理"""
    return 0 <= soc <= 100

def validate_gps_coordinates(latitude: float, longitude: float) -> bool:
    """验证GPS坐标是否合理"""
    return -90.0 <= latitude <= 90.0 and -180.0 <= longitude <= 180.0

def calculate_battery_health(current_capacity: float, design_capacity: float) -> float:
    """计算电池健康度"""
    if design_capacity <= 0:
        return 0.0
    health = (current_capacity / design_capacity) * 100
    return min(100.0, max(0.0, health))

def estimate_remaining_time(current_capacity: float, current_current: float) -> Optional[float]:
    """估算剩余使用时间（小时）"""
    if current_current <= 0:
        return None  # 不在放电状态
    
    remaining_hours = current_capacity / abs(current_current)
    return round(remaining_hours, 2)

def calculate_power(voltage: float, current: float) -> float:
    """计算功率（瓦特）"""
    return voltage * current

def convert_celsius_to_fahrenheit(celsius: float) -> float:
    """摄氏度转华氏度"""
    return (celsius * 9/5) + 32

def convert_fahrenheit_to_celsius(fahrenheit: float) -> float:
    """华氏度转摄氏度"""
    return (fahrenheit - 32) * 5/9

def format_capacity(capacity_ah: float) -> str:
    """格式化容量显示"""
    if capacity_ah >= 1000:
        return f"{capacity_ah/1000:.1f}kAh"
    else:
        return f"{capacity_ah:.1f}Ah"

def format_voltage(voltage: float) -> str:
    """格式化电压显示"""
    return f"{voltage:.2f}V"

def format_current(current: float) -> str:
    """格式化电流显示"""
    if current >= 0:
        return f"+{current:.2f}A"
    else:
        return f"{current:.2f}A"

def format_temperature(temperature: float, unit: str = 'C') -> str:
    """格式化温度显示"""
    if unit.upper() == 'F':
        temp = convert_celsius_to_fahrenheit(temperature)
        return f"{temp:.1f}°F"
    else:
        return f"{temperature:.1f}°C"

def format_percentage(value: float) -> str:
    """格式化百分比显示"""
    return f"{value:.1f}%"

def create_api_response(success: bool = True, message: str = "", code: int = 200, 
                       result: Any = None, **kwargs) -> Dict:
    """创建标准API响应格式"""
    response = {
        'success': success,
        'message': message,
        'code': code,
        'timestamp': get_current_timestamp()
    }
    
    if result is not None:
        response['result'] = result
    
    response.update(kwargs)
    return response

def parse_json_safely(json_str: str, default: Any = None) -> Any:
    """安全解析JSON字符串"""
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return default

def encode_base64(data: str) -> str:
    """Base64编码"""
    return base64.b64encode(data.encode('utf-8')).decode('utf-8')

def decode_base64(encoded_data: str) -> str:
    """Base64解码"""
    try:
        return base64.b64decode(encoded_data).decode('utf-8')
    except Exception:
        return ""

def calculate_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """计算两点间距离（公里）"""
    import math
    
    # 转换为弧度
    lat1_rad = math.radians(lat1)
    lon1_rad = math.radians(lon1)
    lat2_rad = math.radians(lat2)
    lon2_rad = math.radians(lon2)
    
    # Haversine公式
    dlat = lat2_rad - lat1_rad
    dlon = lon2_rad - lon1_rad
    
    a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))
    
    # 地球半径（公里）
    r = 6371
    
    return r * c

def get_battery_status_text(status_code: int) -> str:
    """获取电池状态文本"""
    status_map = {
        0: '关闭',
        1: '开启',
        2: '故障',
        3: '未知'
    }
    return status_map.get(status_code, '未知')

def get_protection_level_text(level: int) -> str:
    """获取保护级别文本"""
    level_map = {
        0: '正常',
        1: '警告',
        2: '保护',
        3: '严重'
    }
    return level_map.get(level, '未知')

def validate_control_command(cmd_id: str) -> bool:
    """验证控制命令是否有效"""
    valid_commands = ['40', '41', '08', '09']
    return cmd_id in valid_commands

def get_command_description(cmd_id: str) -> str:
    """获取命令描述"""
    descriptions = {
        '40': '关闭放电MOS',
        '41': '打开放电MOS',
        '08': '打开充/放电MOS（同时）',
        '09': '关闭充/放电MOS（同时）'
    }
    return descriptions.get(cmd_id, '未知命令')

def generate_mock_jwt_token(user_id: str, client_id: str) -> str:
    """生成模拟JWT Token（仅用于演示）"""
    import jwt
    import time
    
    payload = {
        'user_id': user_id,
        'client_id': client_id,
        'exp': int(time.time()) + 86400,  # 24小时过期
        'iat': int(time.time())
    }
    
    # 使用简单的密钥（生产环境应使用安全的密钥）
    secret_key = 'lithium-monitor-secret-key-2025'
    
    try:
        token = jwt.encode(payload, secret_key, algorithm='HS256')
        return token
    except Exception:
        return ""

def log_api_request(endpoint: str, method: str, params: Dict = None, 
                   response_code: int = 200, duration: float = 0.0):
    """记录API请求日志"""
    log_entry = {
        'timestamp': get_current_timestamp(),
        'endpoint': endpoint,
        'method': method,
        'params': params or {},
        'response_code': response_code,
        'duration_ms': round(duration * 1000, 2)
    }
    
    # 这里可以集成实际的日志系统
    print(f"API Log: {json.dumps(log_entry, ensure_ascii=False)}")

def create_error_response(message: str, code: int = 400, **kwargs) -> Dict:
    """创建错误响应"""
    return create_api_response(
        success=False,
        message=message,
        code=code,
        **kwargs
    )

def create_success_response(message: str = "操作成功", result: Any = None, **kwargs) -> Dict:
    """创建成功响应"""
    return create_api_response(
        success=True,
        message=message,
        code=200,
        result=result,
        **kwargs
    )

class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_battery_data(data: Dict) -> List[str]:
        """验证电池数据"""
        errors = []
        
        # 验证电压
        if 'voltage' in data:
            if not validate_voltage(data['voltage']):
                errors.append('电压值超出合理范围')
        
        # 验证电流
        if 'current' in data:
            if not validate_current(data['current']):
                errors.append('电流值超出合理范围')
        
        # 验证温度
        if 'temperature' in data:
            if not validate_temperature(data['temperature']):
                errors.append('温度值超出合理范围')
        
        # 验证SOC
        if 'soc' in data:
            if not validate_soc(data['soc']):
                errors.append('SOC值超出合理范围')
        
        return errors
    
    @staticmethod
    def validate_gps_data(data: Dict) -> List[str]:
        """验证GPS数据"""
        errors = []
        
        if 'latitude' in data and 'longitude' in data:
            if not validate_gps_coordinates(data['latitude'], data['longitude']):
                errors.append('GPS坐标超出合理范围')
        
        return errors

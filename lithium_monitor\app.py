#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from flask import Flask, render_template, jsonify
from flask_cors import CORS
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def create_app(config_name=None):
    """应用工厂函数"""
    app = Flask(__name__)
    
    # 加载配置
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    from config import config
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    CORS(app, origins=app.config['CORS_ORIGINS'])
    
    # 注册蓝图
    register_blueprints(app)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    # 注册主路由
    register_main_routes(app)
    
    return app

def register_blueprints(app):
    """注册蓝图"""
    try:
        # 数据API蓝图
        from api.battery_data import battery_data_bp
        app.register_blueprint(battery_data_bp, url_prefix='/api/battery')
        
        # 控制API蓝图
        from api.control import control_bp
        app.register_blueprint(control_bp, url_prefix='/api/control')
        
        # 认证API蓝图
        from api.auth import auth_bp
        app.register_blueprint(auth_bp, url_prefix='/api/auth')
        
    except ImportError as e:
        print(f"警告: 无法导入蓝图 - {e}")
        print("这是正常的，因为API模块还未创建")

def register_error_handlers(app):
    """注册错误处理器"""
    
    @app.errorhandler(404)
    def not_found_error(error):
        return jsonify({
            'success': False,
            'message': '页面未找到',
            'code': 404
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return jsonify({
            'success': False,
            'message': '服务器内部错误',
            'code': 500
        }), 500
    
    @app.errorhandler(403)
    def forbidden_error(error):
        return jsonify({
            'success': False,
            'message': '访问被禁止',
            'code': 403
        }), 403

def register_main_routes(app):
    """注册主要路由"""
    
    @app.route('/')
    def index():
        """首页 - 重定向到监控仪表板"""
        return render_template('dashboard.html')
    
    @app.route('/dashboard')
    def dashboard():
        """监控仪表板页面"""
        return render_template('dashboard.html')
    
    @app.route('/control')
    def control():
        """控制页面"""
        return render_template('control.html')
    
    @app.route('/health')
    def health_check():
        """健康检查接口"""
        return jsonify({
            'success': True,
            'message': '锂电监控系统运行正常',
            'code': 200,
            'version': '1.0.0',
            'timestamp': int(os.times().elapsed * 1000)
        })
    
    @app.route('/api/config')
    def get_config():
        """获取前端配置信息"""
        return jsonify({
            'success': True,
            'result': {
                'battery_models': app.config['BATTERY_MODELS'],
                'control_commands': app.config['CONTROL_COMMANDS'],
                'simulation_interval': app.config['BATTERY_CONFIG']['SIMULATION_INTERVAL'],
                'client_id': app.config['BATTERY_CONFIG']['DEFAULT_CLIENT_ID']
            },
            'code': 200
        })

# 创建应用实例
app = create_app()

if __name__ == '__main__':
    # 开发服务器配置
    debug_mode = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    port = int(os.environ.get('PORT', 5000))
    host = os.environ.get('HOST', '127.0.0.1')
    
    print("=" * 60)
    print("🔋 锂电监控系统启动中...")
    print(f"🌐 访问地址: http://{host}:{port}")
    print(f"📊 监控仪表板: http://{host}:{port}/dashboard")
    print(f"🎛️ 控制面板: http://{host}:{port}/control")
    print(f"💚 健康检查: http://{host}:{port}/health")
    print("=" * 60)
    
    app.run(
        host=host,
        port=port,
        debug=debug_mode,
        threaded=True
    )

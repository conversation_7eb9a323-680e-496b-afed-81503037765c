#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import xml.etree.ElementTree as ET
import re
import urllib.parse
import json
from typing import Dict, List, Tuple

class RealServerAPIAnalyzer:
    """分析XML中的万洋锂电真实服务器API接口"""
    
    def __init__(self, xml_file: str = "锂电.xml"):
        self.xml_file = xml_file
        self.real_apis = {}
        self.server_base = "https://sys.wyzxcn.com/jeecg-boot"
        
    def analyze_real_apis(self):
        """分析真实的API接口"""
        print("🔍 分析万洋锂电服务器的真实API接口...")
        print("=" * 80)
        
        try:
            tree = ET.parse(self.xml_file)
            root = tree.getroot()
            
            # 查找所有HTTP请求
            self.find_http_requests(root)
            
            # 分析API接口模式
            self.analyze_api_patterns()
            
            # 提取认证信息
            self.extract_auth_info()
            
            # 生成API代理方案
            self.generate_proxy_solution()
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
    
    def find_http_requests(self, root):
        """查找HTTP请求"""
        print("\n📡 查找真实服务器API请求...")
        
        # 查找所有包含万洋锂电服务器URL的请求
        wanyang_urls = []
        
        for elem in root.iter():
            if elem.text:
                # 查找万洋锂电服务器URL
                urls = re.findall(r'https://sys\.wyzxcn\.com[^\s<>"\']+', elem.text)
                wanyang_urls.extend(urls)
        
        # 去重并分析
        unique_urls = list(set(wanyang_urls))
        print(f"找到 {len(unique_urls)} 个万洋锂电服务器API请求:")
        
        for i, url in enumerate(unique_urls[:10], 1):  # 显示前10个
            print(f"  {i}. {url}")
            self.analyze_single_api(url)
    
    def analyze_single_api(self, url: str):
        """分析单个API接口"""
        try:
            parsed_url = urllib.parse.urlparse(url)
            path = parsed_url.path
            params = urllib.parse.parse_qs(parsed_url.query)
            
            # 提取API信息
            api_info = {
                'full_url': url,
                'path': path,
                'params': params,
                'method': 'GET'  # 从XML看大部分是GET请求
            }
            
            # 根据路径分类API
            if '/fnjbattery/realTime' in path:
                self.real_apis['realtime'] = api_info
                print(f"    📊 实时数据API: {path}")
                
            elif '/fnjbattery/parseAlarmAndBattery' in path:
                self.real_apis['parse_alarm'] = api_info
                print(f"    ⚠️ 警报解析API: {path}")
                
            elif '/fnjbattery/parseProtectionAndWarning' in path:
                self.real_apis['parse_protection'] = api_info
                print(f"    🛡️ 保护解析API: {path}")
                
            elif '/fnjbattery/deviceParameters' in path:
                self.real_apis['device_params'] = api_info
                print(f"    ⚙️ 设备参数API: {path}")
                
            elif '/fnjbattery/terminalControl' in path:
                self.real_apis['control'] = api_info
                print(f"    🎛️ 终端控制API: {path}")
                
            elif '/wybattery/wyBatteryInfor/list' in path:
                self.real_apis['battery_list'] = api_info
                print(f"    📋 电池列表API: {path}")
                
        except Exception as e:
            print(f"    ❌ 解析API失败: {e}")
    
    def analyze_api_patterns(self):
        """分析API模式"""
        print(f"\n🔗 发现的真实API接口:")
        
        for api_name, api_info in self.real_apis.items():
            print(f"\n  📌 {api_name.upper()}:")
            print(f"    路径: {api_info['path']}")
            print(f"    方法: {api_info['method']}")
            
            if api_info['params']:
                print(f"    参数:")
                for param, values in api_info['params'].items():
                    print(f"      {param}: {values[0] if values else 'N/A'}")
    
    def extract_auth_info(self):
        """提取认证信息"""
        print(f"\n🔐 提取认证信息...")
        
        # 从API参数中提取认证信息
        auth_info = {}
        
        for api_name, api_info in self.real_apis.items():
            params = api_info.get('params', {})
            
            if 'key' in params:
                auth_info['key'] = params['key'][0]
            if 'clientId' in params:
                auth_info['clientId'] = params['clientId'][0]
            if 'userId' in params:
                auth_info['userId'] = params['userId'][0]
        
        if auth_info:
            print("  找到认证信息:")
            for key, value in auth_info.items():
                print(f"    {key}: {value}")
            
            self.auth_info = auth_info
        else:
            print("  ❌ 未找到认证信息")
    
    def generate_proxy_solution(self):
        """生成API代理解决方案"""
        print(f"\n🚀 生成真实API代理解决方案...")
        
        print(f"\n📋 需要实现的代理接口:")
        
        proxy_apis = [
            {
                'local_path': '/api/battery/realtime',
                'real_api': 'realtime',
                'description': '实时电池数据',
                'method': 'GET'
            },
            {
                'local_path': '/api/control/terminal',
                'real_api': 'control', 
                'description': '终端控制命令',
                'method': 'GET'
            },
            {
                'local_path': '/api/battery/device-parameters',
                'real_api': 'device_params',
                'description': '设备参数',
                'method': 'GET'
            },
            {
                'local_path': '/api/battery/models',
                'real_api': 'battery_list',
                'description': '电池型号列表',
                'method': 'GET'
            }
        ]
        
        for proxy in proxy_apis:
            real_api = self.real_apis.get(proxy['real_api'])
            if real_api:
                print(f"\n  ✅ {proxy['description']}:")
                print(f"    本地路径: {proxy['local_path']}")
                print(f"    真实API: {self.server_base}{real_api['path']}")
                print(f"    代理方式: 转发请求到真实服务器")
            else:
                print(f"\n  ❌ {proxy['description']}: 未找到对应的真实API")
        
        print(f"\n💡 实现方案:")
        solutions = [
            "1. 创建API代理服务，转发请求到万洋锂电服务器",
            "2. 保持原有的本地API路径，用户无感知切换",
            "3. 添加请求缓存和错误处理机制",
            "4. 实现真实的控制命令发送",
            "5. 添加实时数据轮询和推送功能"
        ]
        
        for solution in solutions:
            print(f"  {solution}")
        
        print(f"\n⚠️ 注意事项:")
        warnings = [
            "• 需要处理跨域请求问题",
            "• 需要保持认证信息的有效性", 
            "• 需要处理网络异常和超时",
            "• 需要考虑请求频率限制",
            "• 需要确保数据格式兼容性"
        ]
        
        for warning in warnings:
            print(f"  {warning}")

def main():
    analyzer = RealServerAPIAnalyzer()
    analyzer.analyze_real_apis()

if __name__ == "__main__":
    main()

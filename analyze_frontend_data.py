#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
from typing import Dict, List

def analyze_realtime_data():
    """分析实时数据结构，为前端设计提供依据"""
    
    print("🔍 分析真实数据结构，设计前端界面布局...")
    
    try:
        # 获取实时数据
        response = requests.get(
            'http://127.0.0.1:5000/api/battery/realtime',
            params={
                'key': '79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c',
                'clientId': '380074209785'
            }
        )
        
        if response.status_code != 200:
            print(f"❌ API请求失败: {response.status_code}")
            return
        
        data = response.json()
        detail = json.loads(data['detail'])
        
        print("=" * 80)
        print("📊 真实数据结构分析报告")
        print("=" * 80)
        
        # 分析主要数据字段
        print("\n🔹 主要数据字段:")
        main_fields = {
            'mobile': '设备手机号',
            'heartBeatTime': '心跳时间',
            'latitude': '纬度',
            'longitude': '经度',
            'altitude': '海拔',
            'speed': '速度',
            'direction': '方向',
            'batteryId': '电池ID',
            'batteryPackageInfo': '电池包信息',
            'bmsStatusInfo': 'BMS状态信息',
            'baseStationInfo': '基站信息'
        }
        
        for field, desc in main_fields.items():
            if field in detail:
                value = detail[field]
                if isinstance(value, str) and len(value) > 50:
                    print(f"  {field}: {desc} (JSON数据)")
                else:
                    print(f"  {field}: {desc} = {value}")
        
        # 分析电池包信息
        if 'batteryPackageInfo' in detail:
            battery_info = json.loads(detail['batteryPackageInfo'])
            print(f"\n🔋 电池包信息 ({len(battery_info)}个字段):")
            
            key_fields = {
                'totalVoltage': '总电压',
                'totalCurrent': '总电流', 
                'soc': 'SOC电量',
                'BMSTemp': 'BMS温度',
                'cellQuantity': '电芯数量',
                'tempQuantity': '温度传感器数量',
                'residualCapacity': '剩余容量',
                'currentCapacity': '当前容量'
            }
            
            for field, desc in key_fields.items():
                if field in battery_info:
                    print(f"    {field}: {desc} = {battery_info[field]}")
            
            # 分析单体电压
            if 'cellVoltageDetail' in battery_info:
                voltages = battery_info['cellVoltageDetail']
                print(f"    cellVoltageDetail: 单体电压 ({len(voltages)}个)")
                print(f"      范围: {min(voltages)} - {max(voltages)}V")
                print(f"      平均: {sum(float(v) for v in voltages) / len(voltages):.3f}V")
            
            # 分析温度信息
            if 'tempDetailInfo' in battery_info:
                temps = battery_info['tempDetailInfo']
                print(f"    tempDetailInfo: 温度详情 ({len(temps)}个)")
                print(f"      温度值: {', '.join(temps)}°C")
            
            # 分析电池状态
            if 'batteryStatus' in battery_info:
                status = battery_info['batteryStatus']
                print(f"    batteryStatus: 电池状态 ({len(status)}个状态位)")
                key_status = ['chargeMOSStatus', 'disChargeMOSStatus', 'DCMOS', 'chargeMOS']
                for s in key_status:
                    if s in status:
                        print(f"      {s}: {status[s]}")
        
        # 分析BMS状态信息
        if 'bmsStatusInfo' in detail:
            bms_info = json.loads(detail['bmsStatusInfo'])
            print(f"\n⚡ BMS状态信息 ({len(bms_info)}个字段):")
            
            key_bms_fields = {
                'envTemp': '环境温度',
                'alertStatusBit': '警告状态位',
                'protectStatusBit': '保护状态位',
                'chargeMosStatus': '充电MOS状态',
                'disChargeMosStatus': '放电MOS状态',
                'totalMileage': '总里程',
                'averageVol': '平均电压',
                'volDiffer': '电压差'
            }
            
            for field, desc in key_bms_fields.items():
                if field in bms_info:
                    print(f"    {field}: {desc} = {bms_info[field]}")
        
        # 生成前端界面设计建议
        generate_frontend_design_suggestions()
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def generate_frontend_design_suggestions():
    """生成前端界面设计建议"""
    
    print("\n" + "=" * 80)
    print("🎨 前端界面设计建议")
    print("=" * 80)
    
    print("\n📱 界面布局建议:")
    print("""
    ┌─────────────────────────────────────────────────────────────┐
    │                    锂电监控系统                              │
    ├─────────────────────────────────────────────────────────────┤
    │  关键指标卡片区域 (4个卡片)                                  │
    │  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐           │
    │  │总电压   │ │总电流   │ │SOC电量  │ │BMS温度  │           │
    │  │84.40V   │ │0.00A    │ │96%      │ │35°C     │           │
    │  └─────────┘ └─────────┘ └─────────┘ └─────────┘           │
    ├─────────────────────────────────────────────────────────────┤
    │  主要内容区域                                                │
    │  ┌─────────────────────────┐ ┌─────────────────────────┐   │
    │  │  实时数据图表            │ │  设备状态面板            │   │
    │  │  - 电压/电流趋势         │ │  - MOS状态              │   │
    │  │  - 温度变化             │ │  - GPS位置              │   │
    │  │                         │ │  - 设备信息             │   │
    │  └─────────────────────────┘ │  - 警告信息             │   │
    │  ┌─────────────────────────┐ └─────────────────────────┘   │
    │  │  单体电压分布 (20个)     │                             │
    │  │  ████████████████████   │                             │
    │  └─────────────────────────┘                             │
    └─────────────────────────────────────────────────────────────┘
    """)
    
    print("\n🎯 关键显示组件:")
    components = [
        "1. 关键指标卡片 - 总电压、总电流、SOC、BMS温度",
        "2. 实时数据图表 - 电压/电流趋势线图",
        "3. 单体电压分布 - 20个电芯的柱状图",
        "4. MOS状态指示器 - 充电/放电MOS开关状态",
        "5. GPS位置显示 - 经纬度、海拔、速度",
        "6. BMS状态面板 - 保护状态、警告信息",
        "7. 温度监控 - 2个温度传感器数据",
        "8. 设备信息 - 电池ID、心跳时间、在线状态"
    ]
    
    for component in components:
        print(f"  {component}")
    
    print("\n📊 数据更新策略:")
    strategies = [
        "• 关键指标: 每5秒更新一次",
        "• 实时图表: 滚动显示最近20个数据点",
        "• 单体电压: 实时更新，异常电压高亮显示",
        "• MOS状态: 实时同步控制API状态",
        "• GPS位置: 位置变化时更新",
        "• 警告信息: 实时检测并弹窗提示"
    ]
    
    for strategy in strategies:
        print(f"  {strategy}")
    
    print("\n🎨 视觉设计要点:")
    design_points = [
        "• 使用渐变色卡片突出关键指标",
        "• 电压正常(绿色)、警告(黄色)、异常(红色)的状态色彩",
        "• MOS状态使用开关样式的视觉指示器",
        "• 单体电压使用柱状图，异常值突出显示",
        "• 响应式设计，支持移动端查看",
        "• 实时数据使用动画效果增强用户体验"
    ]
    
    for point in design_points:
        print(f"  {point}")

if __name__ == "__main__":
    analyze_realtime_data()

{% extends "base.html" %}

{% block title %}监控仪表板 - 锂电监控系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 页面标题 -->
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-tachometer-alt me-2"></i>实时监控仪表板</h2>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" id="refreshData">
                    <i class="fas fa-sync-alt me-1"></i>刷新数据
                </button>
                <button type="button" class="btn btn-outline-secondary" id="exportData">
                    <i class="fas fa-download me-1"></i>导出数据
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 关键指标卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card card-metric" id="voltageCard">
            <div class="card-body text-center">
                <div class="metric-value" id="totalVoltage">--</div>
                <div class="metric-label">总电压 (V)</div>
                <small class="d-block mt-1" id="voltageStatus">
                    <span class="status-indicator status-online"></span>正常
                </small>
                <div class="metric-detail mt-2">
                    <small class="text-light">范围: <span id="voltageRange">--</span></small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card card-metric" id="currentCard">
            <div class="card-body text-center">
                <div class="metric-value" id="totalCurrent">--</div>
                <div class="metric-label">总电流 (A)</div>
                <small class="d-block mt-1" id="currentStatus">
                    <span class="status-indicator status-online"></span>正常
                </small>
                <div class="metric-detail mt-2">
                    <small class="text-light">状态: <span id="currentDirection">--</span></small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card card-metric" id="socCard">
            <div class="card-body text-center">
                <div class="metric-value" id="socValue">--</div>
                <div class="metric-label">SOC电量 (%)</div>
                <small class="d-block mt-1" id="socStatus">
                    <span class="status-indicator status-online"></span>正常
                </small>
                <div class="metric-detail mt-2">
                    <small class="text-light">容量: <span id="capacityInfo">--</span></small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card card-metric" id="tempCard">
            <div class="card-body text-center">
                <div class="metric-value" id="bmsTemp">--</div>
                <div class="metric-label">BMS温度 (°C)</div>
                <small class="d-block mt-1" id="tempStatus">
                    <span class="status-indicator status-online"></span>正常
                </small>
                <div class="metric-detail mt-2">
                    <small class="text-light">环境: <span id="envTemp">--</span>°C</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 次要指标卡片 -->
<div class="row mb-4">
    <div class="col-xl-2 col-md-4 col-6 mb-3">
        <div class="card bg-light">
            <div class="card-body text-center py-3">
                <h6 class="card-title mb-1">电芯数量</h6>
                <h5 class="text-primary mb-0" id="cellQuantity">--</h5>
            </div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-6 mb-3">
        <div class="card bg-light">
            <div class="card-body text-center py-3">
                <h6 class="card-title mb-1">温度传感器</h6>
                <h5 class="text-info mb-0" id="tempQuantity">--</h5>
            </div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-6 mb-3">
        <div class="card bg-light">
            <div class="card-body text-center py-3">
                <h6 class="card-title mb-1">剩余容量</h6>
                <h5 class="text-success mb-0" id="residualCapacity">--</h5>
            </div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-6 mb-3">
        <div class="card bg-light">
            <div class="card-body text-center py-3">
                <h6 class="card-title mb-1">当前容量</h6>
                <h5 class="text-warning mb-0" id="currentCapacity">--</h5>
            </div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-6 mb-3">
        <div class="card bg-light">
            <div class="card-body text-center py-3">
                <h6 class="card-title mb-1">总里程</h6>
                <h5 class="text-secondary mb-0" id="totalMileage">--</h5>
            </div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-6 mb-3">
        <div class="card bg-light">
            <div class="card-body text-center py-3">
                <h6 class="card-title mb-1">电压差</h6>
                <h5 class="text-danger mb-0" id="volDiffer">--</h5>
            </div>
        </div>
    </div>
</div>

<!-- 图表和详细信息 -->
<div class="row">
    <!-- 实时数据图表 -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>实时数据趋势
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="realtimeChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- 单体电压分布 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-battery-half me-2"></i>单体电压分布
                </h5>
                <div>
                    <span class="badge bg-info me-2">总数: <span id="totalCells">20</span></span>
                    <span class="badge bg-success me-2">最高: <span id="maxVoltage">--</span>V</span>
                    <span class="badge bg-warning">最低: <span id="minVoltage">--</span>V</span>
                </div>
            </div>
            <div class="card-body">
                <!-- 单体电压网格显示 -->
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="cell-voltage-grid" id="cellVoltageGrid">
                            <!-- 20个电芯电压将在这里动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 单体电压图表 -->
                <div class="chart-container">
                    <canvas id="cellVoltageChart"></canvas>
                </div>

                <!-- 电压统计信息 -->
                <div class="row mt-3">
                    <div class="col-md-3 col-6">
                        <div class="text-center">
                            <small class="text-muted">平均电压</small>
                            <div class="fw-bold" id="avgVoltage">--</div>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="text-center">
                            <small class="text-muted">电压差</small>
                            <div class="fw-bold" id="voltageSpread">--</div>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="text-center">
                            <small class="text-muted">异常电芯</small>
                            <div class="fw-bold text-danger" id="abnormalCells">0</div>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="text-center">
                            <small class="text-muted">平衡状态</small>
                            <div class="fw-bold text-info" id="balanceActive">--</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 状态信息面板 -->
    <div class="col-lg-4">
        <!-- 设备状态 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-microchip me-2"></i>设备状态
                </h5>
            </div>
            <div class="card-body">
                <!-- MOS状态 -->
                <div class="row text-center mb-3">
                    <div class="col-6">
                        <div class="border rounded p-3" id="chargeMosPanel">
                            <div class="h6 mb-2">充电MOS</div>
                            <div class="mos-switch" id="chargeMosSwitch">
                                <span class="badge fs-6" id="chargeMosStatus">--</span>
                            </div>
                            <small class="text-muted d-block mt-1">允许充电</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-3" id="dischargeMosPanel">
                            <div class="h6 mb-2">放电MOS</div>
                            <div class="mos-switch" id="dischargeMosSwitch">
                                <span class="badge fs-6" id="dischargeMosStatus">--</span>
                            </div>
                            <small class="text-muted d-block mt-1">允许放电</small>
                        </div>
                    </div>
                </div>

                <!-- BMS状态位 -->
                <div class="mb-3">
                    <h6 class="mb-2">BMS状态</h6>
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">警告状态:</small>
                            <div class="status-bits" id="alertStatusBits">
                                <code class="small">0000000000000000</code>
                            </div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">保护状态:</small>
                            <div class="status-bits" id="protectStatusBits">
                                <code class="small">0000000000000000</code>
                            </div>
                        </div>
                    </div>
                </div>

                <hr>

                <!-- 设备信息 -->
                <div class="small">
                    <div class="d-flex justify-content-between mb-2">
                        <span>设备ID:</span>
                        <span class="text-muted" id="deviceId">--</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>心跳时间:</span>
                        <span class="text-muted" id="heartBeatTime">--</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>BMS版本:</span>
                        <span class="text-muted" id="bmsVersion">--</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>在线状态:</span>
                        <span class="text-success" id="onlineStatus">
                            <i class="fas fa-circle me-1"></i>在线
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 位置信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-satellite-dish me-2"></i>位置与通信
                </h5>
            </div>
            <div class="card-body">
                <!-- GPS信息 -->
                <h6 class="mb-2">GPS定位</h6>
                <div class="small mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span>纬度:</span>
                        <span class="text-muted" id="latitude">--</span>
                    </div>
                    <div class="d-flex justify-content-between mb-1">
                        <span>经度:</span>
                        <span class="text-muted" id="longitude">--</span>
                    </div>
                    <div class="d-flex justify-content-between mb-1">
                        <span>海拔:</span>
                        <span class="text-muted" id="altitude">--</span>
                    </div>
                    <div class="d-flex justify-content-between mb-1">
                        <span>速度:</span>
                        <span class="text-muted" id="speed">--</span>
                    </div>
                    <div class="d-flex justify-content-between mb-1">
                        <span>方向:</span>
                        <span class="text-muted" id="direction">--</span>
                    </div>
                </div>

                <!-- 基站信息 -->
                <h6 class="mb-2">基站信息</h6>
                <div class="small mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span>信号强度:</span>
                        <span class="text-muted" id="gsmSignal">--</span>
                    </div>
                    <div class="d-flex justify-content-between mb-1">
                        <span>卫星数量:</span>
                        <span class="text-muted" id="satNum">--</span>
                    </div>
                    <div class="d-flex justify-content-between mb-1">
                        <span>基站ID:</span>
                        <span class="text-muted" id="cellId">--</span>
                    </div>
                    <div class="d-flex justify-content-between mb-1">
                        <span>信号强度:</span>
                        <span class="text-muted" id="dbm">--</span>
                    </div>
                </div>

                <button class="btn btn-outline-primary btn-sm w-100" id="showMap">
                    <i class="fas fa-map me-1"></i>查看地图位置
                </button>
            </div>
        </div>
        
        <!-- 警告信息 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i>系统状态
                </h5>
                <span class="badge bg-success" id="systemStatusBadge">正常</span>
            </div>
            <div class="card-body">
                <!-- 警告信息 -->
                <div class="mb-3">
                    <h6 class="mb-2">
                        <i class="fas fa-exclamation-triangle text-warning me-1"></i>
                        警告信息
                    </h6>
                    <div id="alertsList">
                        <div class="text-center text-muted">
                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                            <div>无警告信息</div>
                        </div>
                    </div>
                </div>

                <!-- 保护状态 -->
                <div class="mb-3">
                    <h6 class="mb-2">
                        <i class="fas fa-shield-alt text-info me-1"></i>
                        保护状态
                    </h6>
                    <div id="protectionsList">
                        <div class="text-center text-muted">
                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                            <div>无保护激活</div>
                        </div>
                    </div>
                </div>

                <!-- 平衡状态 -->
                <div>
                    <h6 class="mb-2">
                        <i class="fas fa-balance-scale text-secondary me-1"></i>
                        平衡状态
                    </h6>
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">平衡1:</small>
                            <div class="balance-status" id="balanceStatus1">
                                <code class="small">0000110110000111</code>
                            </div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">平衡2:</small>
                            <div class="balance-status" id="balanceStatus2">
                                <code class="small">0000000010101000</code>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let realtimeChart, cellVoltageChart;
let updateInterval;

$(document).ready(function() {
    initializeCharts();
    loadInitialData();
    startAutoUpdate();
    
    // 绑定事件
    $('#refreshData').click(loadRealtimeData);
    $('#exportData').click(exportData);
    $('#showMap').click(showLocationMap);
});

function initializeCharts() {
    // 实时数据图表
    const realtimeCtx = document.getElementById('realtimeChart').getContext('2d');
    realtimeChart = new Chart(realtimeCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '电压 (V)',
                data: [],
                borderColor: window.APP_CONFIG.chartColors.voltage,
                backgroundColor: window.APP_CONFIG.chartColors.voltage + '20',
                yAxisID: 'y'
            }, {
                label: '电流 (A)',
                data: [],
                borderColor: window.APP_CONFIG.chartColors.current,
                backgroundColor: window.APP_CONFIG.chartColors.current + '20',
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: { display: true, text: '电压 (V)' }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: { display: true, text: '电流 (A)' },
                    grid: { drawOnChartArea: false }
                }
            }
        }
    });
    
    // 单体电压图表
    const cellCtx = document.getElementById('cellVoltageChart').getContext('2d');
    cellVoltageChart = new Chart(cellCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: '单体电压 (V)',
                data: [],
                backgroundColor: window.APP_CONFIG.chartColors.voltage + '80',
                borderColor: window.APP_CONFIG.chartColors.voltage,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    min: 3.0,
                    max: 4.5,
                    title: { display: true, text: '电压 (V)' }
                }
            }
        }
    });
}

function loadInitialData() {
    showLoading();
    loadRealtimeData();
}

function loadRealtimeData() {
    $.ajax({
        url: '/api/battery/realtime',
        method: 'GET',
        success: function(response) {
            if (response.code === 200) {
                updateDashboard(response);
            } else {
                showNotification('获取数据失败: ' + response.message, 'error');
            }
        },
        error: function() {
            showNotification('网络连接错误', 'error');
        },
        complete: function() {
            hideLoading();
        }
    });
}

function updateDashboard(response) {
    try {
        const data = JSON.parse(response.detail);
        const batteryInfo = JSON.parse(data.batteryPackageInfo);
        const bmsInfo = JSON.parse(data.bmsStatusInfo);
        const baseStationInfo = JSON.parse(data.baseStationInfo);

        // 更新关键指标
        updateKeyMetrics(batteryInfo);

        // 更新次要指标
        updateSecondaryMetrics(batteryInfo, bmsInfo);

        // 更新MOS状态
        updateMosStatus(batteryInfo.batteryStatus, bmsInfo);

        // 更新位置信息
        updateLocationInfo(data, baseStationInfo);

        // 更新单体电压
        updateCellVoltages(batteryInfo);

        // 更新图表
        updateCharts(batteryInfo);

        // 更新设备信息
        updateDeviceInfo(data, bmsInfo);

        // 更新警告和保护状态
        updateAlertsAndProtections(bmsInfo);

        // 更新最后更新时间
        $('#heartBeatTime').text(formatHeartBeatTime(data.heartBeatTime));

    } catch (error) {
        console.error('解析数据失败:', error);
        showNotification('数据解析失败', 'error');
    }
}

function updateKeyMetrics(batteryInfo) {
    // 更新主要指标
    $('#totalVoltage').text(batteryInfo.totalVoltage);
    $('#totalCurrent').text(batteryInfo.totalCurrent);
    $('#socValue').text(batteryInfo.soc);
    $('#bmsTemp').text(batteryInfo.BMSTemp);

    // 更新指标详情
    $('#voltageRange').text(`${batteryInfo.sMinVol || '--'} - ${batteryInfo.sMaxVol || '--'}V`);

    const current = parseFloat(batteryInfo.totalCurrent);
    if (current > 0) {
        $('#currentDirection').text('充电中');
        $('#currentCard').removeClass('card-metric').addClass('card-metric charging');
    } else if (current < 0) {
        $('#currentDirection').text('放电中');
        $('#currentCard').removeClass('card-metric').addClass('card-metric discharging');
    } else {
        $('#currentDirection').text('静置');
        $('#currentCard').removeClass('charging discharging').addClass('card-metric');
    }

    $('#capacityInfo').text(`${batteryInfo.residualCapacity}/${batteryInfo.currentCapacity}Ah`);

    // 更新状态颜色
    updateMetricStatus('voltage', parseFloat(batteryInfo.totalVoltage));
    updateMetricStatus('soc', parseInt(batteryInfo.soc));
    updateMetricStatus('temp', parseInt(batteryInfo.BMSTemp));
}

function updateSecondaryMetrics(batteryInfo, bmsInfo) {
    $('#cellQuantity').text(batteryInfo.cellQuantity);
    $('#tempQuantity').text(batteryInfo.tempQuantity);
    $('#residualCapacity').text(batteryInfo.residualCapacity + 'Ah');
    $('#currentCapacity').text(batteryInfo.currentCapacity + 'Ah');
    $('#totalMileage').text(bmsInfo.totalMileage + 'km');
    $('#volDiffer').text(bmsInfo.volDiffer + 'V');
}

function updateCellVoltages(batteryInfo) {
    const voltages = batteryInfo.cellVoltageDetail;
    const voltageValues = voltages.map(v => parseFloat(v));

    // 计算统计信息
    const maxVoltage = Math.max(...voltageValues);
    const minVoltage = Math.min(...voltageValues);
    const avgVoltage = voltageValues.reduce((a, b) => a + b, 0) / voltageValues.length;
    const voltageSpread = maxVoltage - minVoltage;

    // 更新统计显示
    $('#maxVoltage').text(maxVoltage.toFixed(3));
    $('#minVoltage').text(minVoltage.toFixed(3));
    $('#avgVoltage').text(avgVoltage.toFixed(3) + 'V');
    $('#voltageSpread').text(voltageSpread.toFixed(3) + 'V');

    // 检测异常电芯（偏差超过50mV）
    const abnormalCells = voltageValues.filter(v => Math.abs(v - avgVoltage) > 0.05).length;
    $('#abnormalCells').text(abnormalCells);

    // 生成单体电压网格
    generateCellVoltageGrid(voltages, avgVoltage);

    // 更新图表
    updateCellVoltageChart(voltages);
}

function generateCellVoltageGrid(voltages, avgVoltage) {
    const grid = $('#cellVoltageGrid');
    grid.empty();

    let html = '<div class="row">';
    voltages.forEach((voltage, index) => {
        const voltageValue = parseFloat(voltage);
        const deviation = Math.abs(voltageValue - avgVoltage);

        let statusClass = 'bg-success';
        if (deviation > 0.05) {
            statusClass = 'bg-danger';
        } else if (deviation > 0.03) {
            statusClass = 'bg-warning';
        }

        html += `
            <div class="col-xl-2 col-lg-3 col-md-4 col-6 mb-2">
                <div class="cell-voltage-item border rounded p-2 text-center ${statusClass} text-white">
                    <small class="d-block">电芯${index + 1}</small>
                    <strong>${voltage}V</strong>
                </div>
            </div>
        `;
    });
    html += '</div>';

    grid.html(html);
}

function updateMosStatus(batteryStatus, bmsInfo) {
    const chargeMos = batteryStatus.chargeMOSStatus === '1';
    const dischargeMos = batteryStatus.disChargeMOSStatus === '1';

    // 更新MOS状态显示
    $('#chargeMosStatus')
        .removeClass('bg-success bg-danger')
        .addClass(chargeMos ? 'bg-success' : 'bg-danger')
        .text(chargeMos ? '开启' : '关闭');

    $('#dischargeMosStatus')
        .removeClass('bg-success bg-danger')
        .addClass(dischargeMos ? 'bg-success' : 'bg-danger')
        .text(dischargeMos ? '开启' : '关闭');

    // 更新BMS状态位
    $('#alertStatusBits code').text(bmsInfo.alertStatusBit || '0000000000000000');
    $('#protectStatusBits code').text(bmsInfo.protectStatusBit || '0000000000000000');

    // 更新平衡状态
    $('#balanceStatus1 code').text(bmsInfo.balanceStatus1Bit || '0000110110000111');
    $('#balanceStatus2 code').text(bmsInfo.balanceStatus2Bit || '0000000010101000');

    // 计算平衡激活数量
    const balance1 = bmsInfo.balanceStatus1Bit || '0000110110000111';
    const balance2 = bmsInfo.balanceStatus2Bit || '0000000010101000';
    const activeBalance = (balance1.match(/1/g) || []).length + (balance2.match(/1/g) || []).length;
    $('#balanceActive').text(activeBalance + '个');
}

function updateLocationInfo(data, baseStationInfo) {
    // 更新GPS信息
    $('#latitude').text(parseFloat(data.latitude).toFixed(6));
    $('#longitude').text(parseFloat(data.longitude).toFixed(6));
    $('#altitude').text(data.altitude + 'm');
    $('#speed').text(data.speed + 'km/h');
    $('#direction').text(data.direction + '°');

    // 更新基站信息
    $('#gsmSignal').text(baseStationInfo.GSMSignalIntensity);
    $('#satNum').text(baseStationInfo.locationSatNum);
    $('#cellId').text(baseStationInfo.CELLID);
    $('#dbm').text(baseStationInfo.DBM + 'dBm');
}

function updateDeviceInfo(data, bmsInfo) {
    $('#deviceId').text(data.batteryId);
    $('#bmsVersion').text(data.bmsVersion);

    // 格式化心跳时间
    const heartBeatTime = formatHeartBeatTime(data.heartBeatTime);
    $('#heartBeatTime').text(heartBeatTime);
}

function updateAlertsAndProtections(bmsInfo) {
    // 解析警告状态位
    const alertBits = bmsInfo.alertStatusBit || '0000000000000000';
    const protectBits = bmsInfo.protectStatusBit || '0000000000000000';

    const alerts = parseStatusBits(alertBits, 'alert');
    const protections = parseStatusBits(protectBits, 'protect');

    // 更新警告列表
    updateAlertsList(alerts);

    // 更新保护列表
    updateProtectionsList(protections);

    // 更新系统状态徽章
    updateSystemStatusBadge(alerts, protections);
}

function parseStatusBits(bits, type) {
    const results = [];

    // 简化的状态位解析（实际应根据BMS协议文档）
    if (type === 'alert') {
        if (bits.charAt(15) === '1') results.push('电压警告');
        if (bits.charAt(14) === '1') results.push('电流警告');
        if (bits.charAt(13) === '1') results.push('温度警告');
        if (bits.charAt(12) === '1') results.push('通信警告');
    } else if (type === 'protect') {
        if (bits.charAt(15) === '1') results.push('过压保护');
        if (bits.charAt(14) === '1') results.push('过流保护');
        if (bits.charAt(13) === '1') results.push('过温保护');
        if (bits.charAt(12) === '1') results.push('短路保护');
    }

    return results;
}

function updateAlertsList(alerts) {
    const container = $('#alertsList');

    if (alerts.length === 0) {
        container.html(`
            <div class="text-center text-muted">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <div>无警告信息</div>
            </div>
        `);
    } else {
        let html = '';
        alerts.forEach(alert => {
            html += `
                <div class="alert alert-warning alert-sm py-2 mb-2">
                    <i class="fas fa-exclamation-triangle me-2"></i>${alert}
                </div>
            `;
        });
        container.html(html);
    }
}

function updateProtectionsList(protections) {
    const container = $('#protectionsList');

    if (protections.length === 0) {
        container.html(`
            <div class="text-center text-muted">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <div>无保护激活</div>
            </div>
        `);
    } else {
        let html = '';
        protections.forEach(protection => {
            html += `
                <div class="alert alert-danger alert-sm py-2 mb-2">
                    <i class="fas fa-shield-alt me-2"></i>${protection}
                </div>
            `;
        });
        container.html(html);
    }
}

function updateSystemStatusBadge(alerts, protections) {
    const badge = $('#systemStatusBadge');

    if (protections.length > 0) {
        badge.removeClass('bg-success bg-warning').addClass('bg-danger').text('保护');
    } else if (alerts.length > 0) {
        badge.removeClass('bg-success bg-danger').addClass('bg-warning').text('警告');
    } else {
        badge.removeClass('bg-warning bg-danger').addClass('bg-success').text('正常');
    }
}

function updateCharts(batteryInfo) {
    const now = new Date().toLocaleTimeString();

    // 更新实时数据图表
    if (realtimeChart.data.labels.length > 20) {
        realtimeChart.data.labels.shift();
        realtimeChart.data.datasets[0].data.shift();
        realtimeChart.data.datasets[1].data.shift();
    }

    realtimeChart.data.labels.push(now);
    realtimeChart.data.datasets[0].data.push(parseFloat(batteryInfo.totalVoltage));
    realtimeChart.data.datasets[1].data.push(parseFloat(batteryInfo.totalCurrent));
    realtimeChart.update('none');
}

function updateCellVoltageChart(voltages) {
    const cellLabels = voltages.map((_, index) => `电芯${index + 1}`);
    const cellVoltages = voltages.map(v => parseFloat(v));

    // 计算颜色（基于电压偏差）
    const avgVoltage = cellVoltages.reduce((a, b) => a + b, 0) / cellVoltages.length;
    const colors = cellVoltages.map(voltage => {
        const deviation = Math.abs(voltage - avgVoltage);
        if (deviation > 0.05) return '#dc3545'; // 红色 - 异常
        if (deviation > 0.03) return '#ffc107'; // 黄色 - 警告
        return '#28a745'; // 绿色 - 正常
    });

    cellVoltageChart.data.labels = cellLabels;
    cellVoltageChart.data.datasets[0].data = cellVoltages;
    cellVoltageChart.data.datasets[0].backgroundColor = colors;
    cellVoltageChart.data.datasets[0].borderColor = colors;
    cellVoltageChart.update('none');
}

function formatHeartBeatTime(timeStr) {
    // 格式：YYMMDDHHMMSS -> YYYY-MM-DD HH:MM:SS
    if (!timeStr || timeStr.length !== 12) return '--';

    const year = '20' + timeStr.substr(0, 2);
    const month = timeStr.substr(2, 2);
    const day = timeStr.substr(4, 2);
    const hour = timeStr.substr(6, 2);
    const minute = timeStr.substr(8, 2);
    const second = timeStr.substr(10, 2);

    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
}

function updateMetricStatus(type, value) {
    let statusElement, statusClass, statusText;

    switch(type) {
        case 'voltage':
            statusElement = $('#voltageStatus .status-indicator');
            if (value < 60 || value > 90) {
                statusClass = 'status-warning';
                statusText = '异常';
            } else {
                statusClass = 'status-online';
                statusText = '正常';
            }
            break;

        case 'soc':
            statusElement = $('#socStatus .status-indicator');
            if (value < 20) {
                statusClass = 'status-warning';
                statusText = '低电量';
            } else if (value < 10) {
                statusClass = 'status-offline';
                statusText = '极低';
            } else {
                statusClass = 'status-online';
                statusText = '正常';
            }
            break;

        case 'temp':
            statusElement = $('#tempStatus .status-indicator');
            if (value > 60 || value < -10) {
                statusClass = 'status-offline';
                statusText = '异常';
            } else if (value > 45 || value < 0) {
                statusClass = 'status-warning';
                statusText = '警告';
            } else {
                statusClass = 'status-online';
                statusText = '正常';
            }
            break;
    }

    if (statusElement) {
        statusElement.removeClass('status-online status-warning status-offline').addClass(statusClass);
        statusElement.parent().contents().last()[0].textContent = statusText;
    }
}

function startAutoUpdate() {
    updateInterval = setInterval(loadRealtimeData, window.APP_CONFIG.updateInterval);
}

function stopAutoUpdate() {
    if (updateInterval) {
        clearInterval(updateInterval);
    }
}

function exportData() {
    showNotification('数据导出功能开发中...', 'info');
}

function showLocationMap() {
    showNotification('地图功能开发中...', 'info');
}

// 页面卸载时停止自动更新
$(window).on('beforeunload', function() {
    stopAutoUpdate();
});
</script>
{% endblock %}

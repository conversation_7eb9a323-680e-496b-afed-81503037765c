{% extends "base.html" %}

{% block title %}监控仪表板 - 锂电监控系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 页面标题 -->
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-tachometer-alt me-2"></i>实时监控仪表板</h2>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" id="refreshData">
                    <i class="fas fa-sync-alt me-1"></i>刷新数据
                </button>
                <button type="button" class="btn btn-outline-secondary" id="exportData">
                    <i class="fas fa-download me-1"></i>导出数据
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 关键指标卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card card-metric">
            <div class="card-body text-center">
                <div class="metric-value" id="totalVoltage">--</div>
                <div class="metric-label">总电压 (V)</div>
                <small class="d-block mt-1" id="voltageStatus">
                    <span class="status-indicator status-online"></span>正常
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card card-metric">
            <div class="card-body text-center">
                <div class="metric-value" id="totalCurrent">--</div>
                <div class="metric-label">总电流 (A)</div>
                <small class="d-block mt-1" id="currentStatus">
                    <span class="status-indicator status-online"></span>正常
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card card-metric">
            <div class="card-body text-center">
                <div class="metric-value" id="socValue">--</div>
                <div class="metric-label">电量 (%)</div>
                <small class="d-block mt-1" id="socStatus">
                    <span class="status-indicator status-online"></span>正常
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card card-metric">
            <div class="card-body text-center">
                <div class="metric-value" id="bmsTemp">--</div>
                <div class="metric-label">BMS温度 (°C)</div>
                <small class="d-block mt-1" id="tempStatus">
                    <span class="status-indicator status-online"></span>正常
                </small>
            </div>
        </div>
    </div>
</div>

<!-- 图表和详细信息 -->
<div class="row">
    <!-- 实时数据图表 -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>实时数据趋势
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="realtimeChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- 单体电压分布 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-battery-half me-2"></i>单体电压分布
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="cellVoltageChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 状态信息面板 -->
    <div class="col-lg-4">
        <!-- 设备状态 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>设备状态
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <div class="h6 mb-1">充电MOS</div>
                            <span class="badge bg-success" id="chargeMosStatus">开启</span>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <div class="h6 mb-1">放电MOS</div>
                            <span class="badge bg-success" id="dischargeMosStatus">开启</span>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="small">
                    <div class="d-flex justify-content-between mb-2">
                        <span>设备ID:</span>
                        <span class="text-muted" id="deviceId">--</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>最后更新:</span>
                        <span class="text-muted" id="lastUpdate">--</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>在线状态:</span>
                        <span class="text-success" id="onlineStatus">
                            <i class="fas fa-circle me-1"></i>在线
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 位置信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-map-marker-alt me-2"></i>位置信息
                </h5>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="d-flex justify-content-between mb-2">
                        <span>纬度:</span>
                        <span class="text-muted" id="latitude">--</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>经度:</span>
                        <span class="text-muted" id="longitude">--</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>海拔:</span>
                        <span class="text-muted" id="altitude">--</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>速度:</span>
                        <span class="text-muted" id="speed">--</span>
                    </div>
                </div>
                <button class="btn btn-outline-primary btn-sm w-100 mt-2" id="showMap">
                    <i class="fas fa-map me-1"></i>查看地图
                </button>
            </div>
        </div>
        
        <!-- 警告信息 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>警告信息
                </h5>
            </div>
            <div class="card-body">
                <div id="alertsList">
                    <div class="text-center text-muted">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <div>系统运行正常</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let realtimeChart, cellVoltageChart;
let updateInterval;

$(document).ready(function() {
    initializeCharts();
    loadInitialData();
    startAutoUpdate();
    
    // 绑定事件
    $('#refreshData').click(loadRealtimeData);
    $('#exportData').click(exportData);
    $('#showMap').click(showLocationMap);
});

function initializeCharts() {
    // 实时数据图表
    const realtimeCtx = document.getElementById('realtimeChart').getContext('2d');
    realtimeChart = new Chart(realtimeCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '电压 (V)',
                data: [],
                borderColor: window.APP_CONFIG.chartColors.voltage,
                backgroundColor: window.APP_CONFIG.chartColors.voltage + '20',
                yAxisID: 'y'
            }, {
                label: '电流 (A)',
                data: [],
                borderColor: window.APP_CONFIG.chartColors.current,
                backgroundColor: window.APP_CONFIG.chartColors.current + '20',
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: { display: true, text: '电压 (V)' }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: { display: true, text: '电流 (A)' },
                    grid: { drawOnChartArea: false }
                }
            }
        }
    });
    
    // 单体电压图表
    const cellCtx = document.getElementById('cellVoltageChart').getContext('2d');
    cellVoltageChart = new Chart(cellCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: '单体电压 (V)',
                data: [],
                backgroundColor: window.APP_CONFIG.chartColors.voltage + '80',
                borderColor: window.APP_CONFIG.chartColors.voltage,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    min: 3.0,
                    max: 4.5,
                    title: { display: true, text: '电压 (V)' }
                }
            }
        }
    });
}

function loadInitialData() {
    showLoading();
    loadRealtimeData();
}

function loadRealtimeData() {
    $.ajax({
        url: '/api/battery/realtime',
        method: 'GET',
        success: function(response) {
            if (response.code === 200) {
                updateDashboard(response);
            } else {
                showNotification('获取数据失败: ' + response.message, 'error');
            }
        },
        error: function() {
            showNotification('网络连接错误', 'error');
        },
        complete: function() {
            hideLoading();
        }
    });
}

function updateDashboard(response) {
    try {
        const data = JSON.parse(response.detail);
        const batteryInfo = JSON.parse(data.batteryPackageInfo);
        
        // 更新关键指标
        $('#totalVoltage').text(batteryInfo.totalVoltage);
        $('#totalCurrent').text(batteryInfo.totalCurrent);
        $('#socValue').text(batteryInfo.soc);
        $('#bmsTemp').text(batteryInfo.BMSTemp);
        
        // 更新MOS状态
        updateMosStatus(batteryInfo.batteryStatus);
        
        // 更新位置信息
        updateLocationInfo(data);
        
        // 更新图表
        updateCharts(batteryInfo);
        
        // 更新设备信息
        updateDeviceInfo(data);
        
        // 更新最后更新时间
        $('#lastUpdate').text(new Date().toLocaleString());
        
    } catch (error) {
        console.error('解析数据失败:', error);
        showNotification('数据解析失败', 'error');
    }
}

function updateMosStatus(batteryStatus) {
    const chargeMos = batteryStatus.chargeMOSStatus === '1';
    const dischargeMos = batteryStatus.disChargeMOSStatus === '1';
    
    $('#chargeMosStatus')
        .removeClass('bg-success bg-danger')
        .addClass(chargeMos ? 'bg-success' : 'bg-danger')
        .text(chargeMos ? '开启' : '关闭');
        
    $('#dischargeMosStatus')
        .removeClass('bg-success bg-danger')
        .addClass(dischargeMos ? 'bg-success' : 'bg-danger')
        .text(dischargeMos ? '开启' : '关闭');
}

function updateLocationInfo(data) {
    $('#latitude').text(parseFloat(data.latitude).toFixed(6));
    $('#longitude').text(parseFloat(data.longitude).toFixed(6));
    $('#altitude').text(data.altitude + 'm');
    $('#speed').text(data.speed + 'km/h');
}

function updateCharts(batteryInfo) {
    const now = new Date().toLocaleTimeString();
    
    // 更新实时数据图表
    if (realtimeChart.data.labels.length > 20) {
        realtimeChart.data.labels.shift();
        realtimeChart.data.datasets[0].data.shift();
        realtimeChart.data.datasets[1].data.shift();
    }
    
    realtimeChart.data.labels.push(now);
    realtimeChart.data.datasets[0].data.push(parseFloat(batteryInfo.totalVoltage));
    realtimeChart.data.datasets[1].data.push(parseFloat(batteryInfo.totalCurrent));
    realtimeChart.update('none');
    
    // 更新单体电压图表
    const cellLabels = batteryInfo.cellVoltageDetail.map((_, index) => `电芯${index + 1}`);
    const cellVoltages = batteryInfo.cellVoltageDetail.map(v => parseFloat(v));
    
    cellVoltageChart.data.labels = cellLabels;
    cellVoltageChart.data.datasets[0].data = cellVoltages;
    cellVoltageChart.update('none');
}

function updateDeviceInfo(data) {
    $('#deviceId').text(data.batteryId);
}

function startAutoUpdate() {
    updateInterval = setInterval(loadRealtimeData, window.APP_CONFIG.updateInterval);
}

function stopAutoUpdate() {
    if (updateInterval) {
        clearInterval(updateInterval);
    }
}

function exportData() {
    showNotification('数据导出功能开发中...', 'info');
}

function showLocationMap() {
    showNotification('地图功能开发中...', 'info');
}

// 页面卸载时停止自动更新
$(window).on('beforeunload', function() {
    stopAutoUpdate();
});
</script>
{% endblock %}

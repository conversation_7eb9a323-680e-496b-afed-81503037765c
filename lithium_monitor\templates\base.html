<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}锂电监控系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 自定义CSS -->
    <style>
        .card-metric {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
        }
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .chart-container {
            position: relative;
            height: 300px;
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-battery-three-quarters me-2"></i>
                锂电监控系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}" 
                           href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>监控仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'control' %}active{% endif %}" 
                           href="{{ url_for('control') }}">
                            <i class="fas fa-sliders-h me-1"></i>设备控制
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>管理员
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-1"></i>系统设置</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-download me-1"></i>数据导出</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sign-out-alt me-1"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="container-fluid mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p class="mb-0">
                &copy; 2025 锂电监控系统 | 
                <a href="#" class="text-muted">技术支持</a> | 
                <a href="#" class="text-muted">使用帮助</a>
            </p>
        </div>
    </footer>

    <!-- 全局状态指示器 -->
    <div id="connectionStatus" class="position-fixed bottom-0 end-0 m-3">
        <div class="badge bg-success">
            <i class="fas fa-wifi me-1"></i>连接正常
        </div>
    </div>

    <!-- 全局加载指示器 -->
    <div id="loadingIndicator" class="position-fixed top-50 start-50 translate-middle d-none">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/common.js') }}"></script>
    
    {% block extra_scripts %}{% endblock %}

    <script>
        // 全局配置
        window.APP_CONFIG = {
            apiBaseUrl: '/api',
            updateInterval: 5000, // 5秒更新间隔
            chartColors: {
                voltage: '#007bff',
                current: '#28a745',
                temperature: '#dc3545',
                soc: '#ffc107'
            }
        };

        // 页面加载完成后初始化
        $(document).ready(function() {
            // 初始化连接状态检查
            checkConnectionStatus();
            setInterval(checkConnectionStatus, 30000); // 每30秒检查一次连接状态
            
            // 初始化提示工具
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });

        // 检查连接状态
        function checkConnectionStatus() {
            $.ajax({
                url: '/health',
                method: 'GET',
                timeout: 5000,
                success: function(response) {
                    updateConnectionStatus(true);
                },
                error: function() {
                    updateConnectionStatus(false);
                }
            });
        }

        // 更新连接状态显示
        function updateConnectionStatus(isConnected) {
            const statusElement = $('#connectionStatus .badge');
            if (isConnected) {
                statusElement.removeClass('bg-danger').addClass('bg-success');
                statusElement.html('<i class="fas fa-wifi me-1"></i>连接正常');
            } else {
                statusElement.removeClass('bg-success').addClass('bg-danger');
                statusElement.html('<i class="fas fa-wifi me-1"></i>连接异常');
            }
        }

        // 显示加载指示器
        function showLoading() {
            $('#loadingIndicator').removeClass('d-none');
        }

        // 隐藏加载指示器
        function hideLoading() {
            $('#loadingIndicator').addClass('d-none');
        }

        // 显示通知消息
        function showNotification(message, type = 'info') {
            const alertClass = `alert-${type}`;
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x" 
                     style="z-index: 9999; margin-top: 20px;" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('body').append(alertHtml);
            
            // 3秒后自动关闭
            setTimeout(function() {
                $('.alert').alert('close');
            }, 3000);
        }
    </script>
</body>
</html>

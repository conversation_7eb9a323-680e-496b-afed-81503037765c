<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}锂电监控系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 自定义CSS -->
    <style>
        .card-metric {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            transition: all 0.3s ease;
        }
        .card-metric.charging {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .card-metric.discharging {
            background: linear-gradient(135deg, #fd7e14 0%, #e63946 100%);
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .metric-detail {
            font-size: 0.8rem;
            opacity: 0.8;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
            animation: pulse 2s infinite;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        .cell-voltage-grid {
            max-height: 200px;
            overflow-y: auto;
        }

        .cell-voltage-item {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .cell-voltage-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .mos-switch {
            position: relative;
            display: inline-block;
        }

        .mos-switch .badge {
            padding: 8px 16px;
            font-size: 0.9rem;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .status-bits code {
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            color: #495057;
        }

        .balance-status code {
            font-family: 'Courier New', monospace;
            font-size: 0.75rem;
            background: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
            color: #6c757d;
        }

        .alert-sm {
            padding: 0.375rem 0.75rem;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        .card-header .badge {
            font-size: 0.8rem;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .metric-value {
                font-size: 1.5rem;
            }
            .cell-voltage-grid .col-6 {
                flex: 0 0 50%;
                max-width: 50%;
            }
        }

        /* 数据加载动画 */
        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-battery-three-quarters me-2"></i>
                锂电监控系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}" 
                           href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>监控仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'control' %}active{% endif %}" 
                           href="{{ url_for('control') }}">
                            <i class="fas fa-sliders-h me-1"></i>设备控制
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>管理员
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-1"></i>系统设置</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-download me-1"></i>数据导出</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sign-out-alt me-1"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="container-fluid mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p class="mb-0">
                &copy; 2025 锂电监控系统 | 
                <a href="#" class="text-muted">技术支持</a> | 
                <a href="#" class="text-muted">使用帮助</a>
            </p>
        </div>
    </footer>

    <!-- 全局状态指示器 -->
    <div id="connectionStatus" class="position-fixed bottom-0 end-0 m-3">
        <div class="badge bg-success">
            <i class="fas fa-wifi me-1"></i>连接正常
        </div>
    </div>

    <!-- 全局加载指示器 -->
    <div id="loadingIndicator" class="position-fixed top-50 start-50 translate-middle d-none">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/common.js') }}"></script>
    
    {% block extra_scripts %}{% endblock %}

    <script>
        // 全局配置
        window.APP_CONFIG = {
            apiBaseUrl: '/api',
            updateInterval: 5000, // 5秒更新间隔
            chartColors: {
                voltage: '#007bff',
                current: '#28a745',
                temperature: '#dc3545',
                soc: '#ffc107',
                cellVoltage: '#6f42c1'
            },
            // 真实数据配置
            realData: {
                batteryConfig: {
                    defaultClientId: '380074209785',
                    defaultKey: '79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c',
                    cellCount: 20,
                    tempSensorCount: 2
                },
                thresholds: {
                    voltage: { min: 60, max: 90, warning: 5 },
                    current: { max: 100, warning: 80 },
                    temperature: { min: -10, max: 60, warning: 45 },
                    soc: { low: 20, critical: 10 },
                    cellVoltage: { deviation: 0.05, warning: 0.03 }
                }
            }
        };

        // 页面加载完成后初始化
        $(document).ready(function() {
            // 初始化连接状态检查
            checkConnectionStatus();
            setInterval(checkConnectionStatus, 30000); // 每30秒检查一次连接状态
            
            // 初始化提示工具
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });

        // 检查连接状态
        function checkConnectionStatus() {
            $.ajax({
                url: '/health',
                method: 'GET',
                timeout: 5000,
                success: function(response) {
                    updateConnectionStatus(true);
                },
                error: function() {
                    updateConnectionStatus(false);
                }
            });
        }

        // 更新连接状态显示
        function updateConnectionStatus(isConnected) {
            const statusElement = $('#connectionStatus .badge');
            if (isConnected) {
                statusElement.removeClass('bg-danger').addClass('bg-success');
                statusElement.html('<i class="fas fa-wifi me-1"></i>连接正常');
            } else {
                statusElement.removeClass('bg-success').addClass('bg-danger');
                statusElement.html('<i class="fas fa-wifi me-1"></i>连接异常');
            }
        }

        // 显示加载指示器
        function showLoading() {
            $('#loadingIndicator').removeClass('d-none');
        }

        // 隐藏加载指示器
        function hideLoading() {
            $('#loadingIndicator').addClass('d-none');
        }

        // 显示通知消息
        function showNotification(message, type = 'info') {
            const alertClass = `alert-${type}`;
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x" 
                     style="z-index: 9999; margin-top: 20px;" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('body').append(alertHtml);
            
            // 3秒后自动关闭
            setTimeout(function() {
                $('.alert').alert('close');
            }, 3000);
        }
    </script>
</body>
</html>

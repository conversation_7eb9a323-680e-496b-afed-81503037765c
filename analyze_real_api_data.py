#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import xml.etree.ElementTree as ET
import base64
import json
import re
from typing import Dict, List, Tuple

class RealAPIAnalyzer:
    """分析XML抓包数据，找出万洋锂电服务器的真实API接口"""
    
    def __init__(self, xml_file: str = "锂电.xml"):
        self.xml_file = xml_file
        self.api_calls = []
        self.real_data_samples = {}
        
    def analyze_xml_packets(self):
        """分析XML中的网络抓包数据"""
        print("🔍 分析XML抓包数据，查找万洋锂电服务器的真实API接口...")
        print("=" * 80)
        
        try:
            tree = ET.parse(self.xml_file)
            root = tree.getroot()
            
            # 查找所有HTTP请求和响应
            self.find_http_requests(root)
            
            # 分析API接口
            self.analyze_api_endpoints()
            
            # 提取真实数据样本
            self.extract_real_data_samples()
            
            # 对比当前Web数据与真实数据
            self.compare_with_current_data()
            
        except Exception as e:
            print(f"❌ 分析XML文件失败: {e}")
    
    def find_http_requests(self, root):
        """查找HTTP请求和响应"""
        print("\n📡 查找HTTP请求和响应...")
        
        # 查找所有可能的HTTP相关元素
        http_elements = []
        
        # 查找包含HTTP的元素
        for elem in root.iter():
            if elem.text and ('http' in elem.text.lower() or 'api' in elem.text.lower()):
                http_elements.append(elem)
        
        print(f"找到 {len(http_elements)} 个可能的HTTP相关元素")
        
        # 分析每个元素
        for i, elem in enumerate(http_elements[:10]):  # 只分析前10个
            print(f"\n元素 {i+1}:")
            print(f"  标签: {elem.tag}")
            print(f"  属性: {elem.attrib}")
            if elem.text:
                text = elem.text[:200] + "..." if len(elem.text) > 200 else elem.text
                print(f"  内容: {text}")
    
    def analyze_api_endpoints(self):
        """分析API接口"""
        print("\n🔗 分析API接口...")
        
        try:
            tree = ET.parse(self.xml_file)
            root = tree.getroot()
            
            # 查找所有包含URL的元素
            urls = []
            for elem in root.iter():
                if elem.text:
                    # 查找URL模式
                    url_patterns = re.findall(r'https?://[^\s<>"]+', elem.text)
                    urls.extend(url_patterns)
            
            # 去重并分析
            unique_urls = list(set(urls))
            print(f"找到 {len(unique_urls)} 个唯一URL:")
            
            for url in unique_urls:
                print(f"  {url}")
                
                # 分析是否为万洋锂电的API
                if any(keyword in url.lower() for keyword in ['battery', 'bms', 'lithium', 'jeecg']):
                    self.api_calls.append({
                        'url': url,
                        'type': 'potential_api'
                    })
        
        except Exception as e:
            print(f"❌ 分析API接口失败: {e}")
    
    def extract_real_data_samples(self):
        """提取真实数据样本"""
        print("\n📊 提取真实数据样本...")
        
        try:
            tree = ET.parse(self.xml_file)
            root = tree.getroot()
            
            # 查找JSON数据
            json_data = []
            for elem in root.iter():
                if elem.text:
                    # 查找JSON模式
                    json_matches = re.findall(r'\{[^{}]*\}', elem.text)
                    for match in json_matches:
                        try:
                            parsed = json.loads(match)
                            json_data.append(parsed)
                        except:
                            continue
            
            print(f"找到 {len(json_data)} 个JSON数据块")
            
            # 分析JSON数据中的电池相关信息
            battery_data = []
            for data in json_data:
                if self.is_battery_data(data):
                    battery_data.append(data)
            
            print(f"找到 {len(battery_data)} 个电池相关数据块")
            
            # 提取关键数据样本
            if battery_data:
                self.analyze_battery_data_structure(battery_data[0])
        
        except Exception as e:
            print(f"❌ 提取数据样本失败: {e}")
    
    def is_battery_data(self, data: dict) -> bool:
        """判断是否为电池数据"""
        battery_keywords = [
            'voltage', 'current', 'soc', 'temperature', 'battery',
            'cell', 'mos', 'bms', 'charge', 'discharge'
        ]
        
        data_str = json.dumps(data).lower()
        return any(keyword in data_str for keyword in battery_keywords)
    
    def analyze_battery_data_structure(self, data: dict):
        """分析电池数据结构"""
        print("\n🔋 分析真实电池数据结构:")
        
        def print_structure(obj, indent=0):
            prefix = "  " * indent
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if isinstance(value, (dict, list)):
                        print(f"{prefix}{key}: {type(value).__name__}")
                        if isinstance(value, dict) and len(value) < 10:
                            print_structure(value, indent + 1)
                        elif isinstance(value, list) and len(value) > 0:
                            print(f"{prefix}  [{len(value)} items] - 示例: {value[0] if len(value) > 0 else 'empty'}")
                    else:
                        print(f"{prefix}{key}: {value}")
            elif isinstance(obj, list):
                print(f"{prefix}[{len(obj)} items]")
                if obj and len(obj) < 5:
                    for i, item in enumerate(obj):
                        print(f"{prefix}[{i}]: {item}")
        
        print_structure(data)
        
        # 保存真实数据样本
        self.real_data_samples['battery_data'] = data
    
    def compare_with_current_data(self):
        """对比当前Web数据与真实数据"""
        print("\n⚖️ 对比当前Web数据与真实数据...")
        
        try:
            # 获取当前Web数据
            import requests
            response = requests.get(
                'http://127.0.0.1:5000/api/battery/realtime',
                params={
                    'key': '79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c',
                    'clientId': '380074209785'
                }
            )
            
            if response.status_code == 200:
                web_data = response.json()
                detail = json.loads(web_data['detail'])
                battery_info = json.loads(detail['batteryPackageInfo'])
                
                print("📱 当前Web数据:")
                print(f"  总电压: {battery_info.get('totalVoltage')}V")
                print(f"  总电流: {battery_info.get('totalCurrent')}A")
                print(f"  SOC: {battery_info.get('soc')}%")
                print(f"  BMS温度: {battery_info.get('BMSTemp')}°C")
                print(f"  单体电压数量: {len(battery_info.get('cellVoltageDetail', []))}")
                
                # 如果有真实数据样本，进行对比
                if 'battery_data' in self.real_data_samples:
                    real_data = self.real_data_samples['battery_data']
                    print("\n🔍 真实数据样本:")
                    self.print_real_data_comparison(real_data, battery_info)
                
            else:
                print(f"❌ 无法获取当前Web数据: {response.status_code}")
        
        except Exception as e:
            print(f"❌ 对比数据失败: {e}")
    
    def print_real_data_comparison(self, real_data: dict, web_data: dict):
        """打印真实数据与Web数据的对比"""
        
        # 查找可能的电压数据
        voltage_fields = ['voltage', 'totalVoltage', 'vol']
        current_fields = ['current', 'totalCurrent', 'cur']
        soc_fields = ['soc', 'capacity', 'percent']
        temp_fields = ['temperature', 'temp', 'BMSTemp']
        
        def find_field_value(data, field_names):
            for field in field_names:
                for key, value in data.items():
                    if field.lower() in key.lower():
                        return f"{key}: {value}"
            return "未找到"
        
        print("  对比结果:")
        print(f"    电压 - Web: {web_data.get('totalVoltage')}V, 真实: {find_field_value(real_data, voltage_fields)}")
        print(f"    电流 - Web: {web_data.get('totalCurrent')}A, 真实: {find_field_value(real_data, current_fields)}")
        print(f"    SOC - Web: {web_data.get('soc')}%, 真实: {find_field_value(real_data, soc_fields)}")
        print(f"    温度 - Web: {web_data.get('BMSTemp')}°C, 真实: {find_field_value(real_data, temp_fields)}")
    
    def generate_api_analysis_report(self):
        """生成API分析报告"""
        print("\n" + "=" * 80)
        print("📋 万洋锂电API分析报告")
        print("=" * 80)
        
        print(f"\n🔗 发现的API接口:")
        if self.api_calls:
            for i, api in enumerate(self.api_calls, 1):
                print(f"  {i}. {api['url']}")
        else:
            print("  未发现明确的API接口")
        
        print(f"\n📊 数据样本分析:")
        if self.real_data_samples:
            print(f"  找到 {len(self.real_data_samples)} 个数据样本")
            for key, sample in self.real_data_samples.items():
                print(f"  - {key}: {type(sample).__name__}")
        else:
            print("  未找到有效的数据样本")
        
        print(f"\n🎯 数据一致性问题:")
        issues = [
            "• 需要分析真实API的请求格式和认证方式",
            "• 需要确定真实数据的字段名称和数值范围",
            "• 需要同步Web界面与小程序的数据显示",
            "• 需要验证控制命令的真实格式"
        ]
        
        for issue in issues:
            print(f"  {issue}")
        
        print(f"\n💡 建议的解决方案:")
        solutions = [
            "1. 深入分析XML抓包数据，找出完整的API调用链",
            "2. 对比小程序与Web界面的数据差异，找出不一致的字段",
            "3. 调整数据模拟器，使其生成与真实数据一致的数值",
            "4. 实现真实API接口的代理转发功能",
            "5. 添加数据验证机制，确保数据格式正确"
        ]
        
        for solution in solutions:
            print(f"  {solution}")

def main():
    analyzer = RealAPIAnalyzer()
    analyzer.analyze_xml_packets()
    analyzer.generate_api_analysis_report()

if __name__ == "__main__":
    main()

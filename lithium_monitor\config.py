#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from datetime import timedelta

class Config:
    """基础配置类"""
    
    # Flask基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'lithium-monitor-secret-key-2025'
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///lithium_monitor.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # JWT配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-key-lithium-2025'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    
    # 缓存配置
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 300
    
    # 跨域配置
    CORS_ORIGINS = ['*']
    
    # 锂电监控系统特定配置
    BATTERY_CONFIG = {
        'DEFAULT_CLIENT_ID': '380074209785',
        'DEFAULT_KEY': '79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c',
        'DEFAULT_USER_ID': '1933343591078334465',
        'DEFAULT_BATTERY_ID': 'BT2072055010032504140408',
        'SIMULATION_INTERVAL': 5,  # 数据模拟更新间隔（秒）
        'MAX_HISTORY_RECORDS': 1000,  # 最大历史记录数
    }
    
    # API配置
    API_CONFIG = {
        'BASE_URL': 'https://sys.wyzxcn.com/jeecg-boot',
        'TIMEOUT': 30,
        'RETRY_TIMES': 3,
    }
    
    # 控制命令映射
    CONTROL_COMMANDS = {
        '40': '关闭放电MOS',
        '41': '打开放电MOS', 
        '08': '打开充/放电MOS（同时）',
        '09': '关闭充/放电MOS（同时）'
    }
    
    # 电池型号配置
    BATTERY_MODELS = {
        '60V55Ah': {
            'voltage': '71.4V',
            'charge_current': '20A',
            'discharge_current': '60A',
            'size': '220*160*320mm',
            'weight': '20±1KG',
            'waterproof': 'IP65'
        },
        '60V50Ah': {
            'voltage': '71.4V',
            'charge_current': '20A',
            'discharge_current': '60A',
            'size': '175*165*323mm',
            'weight': '15±1KG',
            'waterproof': 'IP65'
        },
        '72V55Ah': {
            'voltage': '86V',
            'charge_current': '20A',
            'discharge_current': '80A',
            'size': '220*160*358mm',
            'weight': '23.8±0.1KG',
            'waterproof': 'IP65'
        },
        '60V43Ah': {
            'voltage': '72V',
            'charge_current': '45A',
            'discharge_current': '60A',
            'size': '210*175*335mm',
            'weight': '21.3±0.1kg',
            'waterproof': 'IP65'
        }
    }

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    TESTING = False
    
    # 开发环境特定配置
    BATTERY_CONFIG = Config.BATTERY_CONFIG.copy()
    BATTERY_CONFIG.update({
        'SIMULATION_INTERVAL': 2,  # 开发环境更快的更新间隔
        'ENABLE_MOCK_DATA': True,
    })

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    TESTING = False
    
    # 生产环境安全配置
    SECRET_KEY = os.environ.get('SECRET_KEY')
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY')
    
    # 生产环境数据库
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')

class TestingConfig(Config):
    """测试环境配置"""
    DEBUG = True
    TESTING = True
    
    # 测试数据库
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    
    # 测试环境特定配置
    BATTERY_CONFIG = Config.BATTERY_CONFIG.copy()
    BATTERY_CONFIG.update({
        'SIMULATION_INTERVAL': 1,  # 测试环境最快更新
        'MAX_HISTORY_RECORDS': 100,
    })

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

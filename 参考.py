#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
万洋锂电池监控系统
基于抓包数据分析构建的真实API接口调用脚本
实现实时数据获取和电池控制操作
"""

import requests
import json
import time
import base64
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('battery_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WanYangBatteryMonitor:
    """万洋锂电池监控类"""
    
    def __init__(self):
        """初始化监控器"""
        # 基础配置
        self.base_url = "https://sys.wyzxcn.com"
        self.session = requests.Session()
        
        # 从抓包数据中提取的认证信息
        self.access_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTA1MTI5MDQsInVzZXJuYW1lIjoid2FueWFuZ19aWnhYbHNxQUZnIn0.js9JxnhkSEzdlsn9mXjr6G2cAr_AZDmezNRmpSeKjpI"
        self.user_id = "1933343591078334465"
        self.client_id = "380074209785"
        self.api_key = "79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c"
        self.battery_code = "BT20720550100325041404408"
        
        # 设置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x67001434) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309c33)XWEB/13639',
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'X-Access-Token': self.access_token,
            'Xweb_xhr': '1',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://servicewechat.com/wxdfb13c626b912abf/12/page-frame.html',
            'Accept-Encoding': 'gzip, deflate',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Priority': 'u=1, i'
        }
        
        self.session.headers.update(self.headers)
        
        # 电池数据缓存
        self.battery_data = {}
        self.last_update = None
        
    def get_user_batteries(self) -> Optional[Dict]:
        """获取用户电池列表"""
        try:
            url = f"{self.base_url}/jeecg-boot/battery/userBattery/api/list"
            params = {'userId': self.user_id}
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            if data.get('success'):
                logger.info("成功获取用户电池列表")
                return data.get('result', [])
            else:
                logger.error(f"获取电池列表失败: {data.get('message')}")
                return None
                
        except Exception as e:
            logger.error(f"获取用户电池列表异常: {e}")
            return None
    
    def get_real_time_data(self) -> Optional[Dict]:
        """获取实时电池数据"""
        try:
            url = f"{self.base_url}/jeecg-boot/fnjbattery/realTime"
            params = {
                'key': self.api_key,
                'clientId': self.client_id
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            if data.get('code') == 200:
                detail = data.get('detail')
                if detail:
                    # 解析JSON字符串
                    battery_info = json.loads(detail)
                    self.battery_data = battery_info
                    self.last_update = datetime.now()
                    logger.info("成功获取实时电池数据")
                    return battery_info
                else:
                    logger.warning("实时数据响应中无详细信息")
                    return None
            else:
                logger.error(f"获取实时数据失败: {data.get('message')}")
                return None
                
        except Exception as e:
            logger.error(f"获取实时数据异常: {e}")
            return None
    
    def parse_battery_package_info(self, package_info_str: str) -> Dict:
        """解析电池包信息"""
        try:
            return json.loads(package_info_str)
        except:
            return {}
    
    def parse_bms_status_info(self, bms_status_str: str) -> Dict:
        """解析BMS状态信息"""
        try:
            return json.loads(bms_status_str)
        except:
            return {}
    
    def get_protection_and_warning(self, bms_status_info: Dict) -> Optional[Dict]:
        """获取保护和警告信息"""
        try:
            url = f"{self.base_url}/jeecg-boot/fnjbattery/parseProtectionAndWarning"
            params = {'bmsStatusInfo': json.dumps(bms_status_info)}
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            if data.get('success'):
                logger.info("成功获取保护和警告信息")
                return data.get('result')
            else:
                logger.error(f"获取保护警告信息失败: {data.get('message')}")
                return None
                
        except Exception as e:
            logger.error(f"获取保护警告信息异常: {e}")
            return None
    
    def get_device_parameters(self) -> Optional[Dict]:
        """获取设备参数"""
        try:
            url = f"{self.base_url}/jeecg-boot/fnjbattery/deviceParameters"
            params = {
                'clientId': self.client_id,
                'key': self.api_key
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            if data.get('code') == 200:
                detail = data.get('detail')
                if detail:
                    device_params = json.loads(detail)
                    logger.info("成功获取设备参数")
                    return device_params
                else:
                    logger.warning("设备参数响应中无详细信息")
                    return None
            else:
                logger.error(f"获取设备参数失败: {data.get('message')}")
                return None
                
        except Exception as e:
            logger.error(f"获取设备参数异常: {e}")
            return None
    
    def get_all_battery_data(self) -> Dict:
        """获取所有电池数据"""
        logger.info("开始获取完整电池数据...")
        
        result = {
            'timestamp': datetime.now().isoformat(),
            'batteries': None,
            'real_time_data': None,
            'device_parameters': None,
            'protection_warnings': None,
            'parsed_data': {}
        }
        
        # 1. 获取电池列表
        batteries = self.get_user_batteries()
        result['batteries'] = batteries
        
        # 2. 获取实时数据
        real_time_data = self.get_real_time_data()
        result['real_time_data'] = real_time_data
        
        if real_time_data:
            # 3. 解析电池包信息
            battery_package_info_str = real_time_data.get('batteryPackageInfo', '{}')
            battery_package_info = self.parse_battery_package_info(battery_package_info_str)
            result['parsed_data']['battery_package'] = battery_package_info
            
            # 4. 解析BMS状态信息
            bms_status_info_str = real_time_data.get('bmsStatusInfo', '{}')
            bms_status_info = self.parse_bms_status_info(bms_status_info_str)
            result['parsed_data']['bms_status'] = bms_status_info
            
            # 5. 获取保护和警告信息
            if bms_status_info:
                protection_warnings = self.get_protection_and_warning(bms_status_info)
                result['protection_warnings'] = protection_warnings
        
        # 6. 获取设备参数
        device_parameters = self.get_device_parameters()
        result['device_parameters'] = device_parameters
        
        return result
    
    def print_battery_summary(self, data: Dict):
        """打印电池数据摘要"""
        print("\n" + "="*70)
        print("🔋 万洋锂电池实时监控数据")
        print("="*70)

        if data.get('real_time_data'):
            real_time = data['real_time_data']

            # 基本信息
            print(f"📱 设备ID: {real_time.get('mobile', 'N/A')}")
            print(f"🔋 电池ID: {real_time.get('batteryId', 'N/A')}")
            print(f"⏰ 心跳时间: {real_time.get('heartBeatTime', 'N/A')}")
            print(f"📍 位置: {real_time.get('latitude', 'N/A')}, {real_time.get('longitude', 'N/A')}")

            # 电池包信息
            battery_package = data['parsed_data'].get('battery_package', {})
            if battery_package:
                print(f"\n⚡ 电池包信息:")
                voltage = float(battery_package.get('totalVoltage', 0))
                current = float(battery_package.get('totalCurrent', 0))
                soc = int(battery_package.get('soc', 0))

                # 使用颜色和图标显示关键信息
                voltage_icon = "🟢" if 70 <= voltage <= 85 else "🟡" if 60 <= voltage < 70 or 85 < voltage <= 90 else "🔴"
                current_icon = "⚡" if abs(current) > 5 else "🔋"
                soc_icon = "🟢" if soc >= 50 else "🟡" if soc >= 20 else "🔴"

                print(f"  {voltage_icon} 总电压: {voltage} V")
                print(f"  {current_icon} 总电流: {current} A")
                print(f"  {soc_icon} SOC: {soc} %")
                print(f"  📊 剩余容量: {battery_package.get('residualCapacity', 'N/A')} Ah")
                print(f"  📈 当前容量: {battery_package.get('currentCapacity', 'N/A')} Ah")
                print(f"  🔄 循环次数: {battery_package.get('loopTimes', 'N/A')}")

                # 单体电压统计
                cell_voltages = battery_package.get('cellVoltageDetail', [])
                if cell_voltages:
                    voltages = [float(v) for v in cell_voltages]
                    max_v, min_v = max(voltages), min(voltages)
                    diff_v = max_v - min_v
                    diff_icon = "🟢" if diff_v <= 0.05 else "🟡" if diff_v <= 0.1 else "🔴"
                    print(f"  {diff_icon} 单体电压 ({len(cell_voltages)}节): 最高{max_v}V, 最低{min_v}V, 差异{diff_v:.3f}V")

                # 温度信息
                temp_details = battery_package.get('tempDetailInfo', [])
                if temp_details:
                    temps = [float(t) for t in temp_details]
                    max_temp = max(temps)
                    temp_icon = "🟢" if max_temp <= 40 else "🟡" if max_temp <= 45 else "🔴"
                    print(f"  {temp_icon} 温度: {', '.join(temp_details)} °C")

                # 充放电状态
                battery_status = battery_package.get('batteryStatus', {})
                if battery_status:
                    charge_status = "🔌 充电中" if battery_status.get('charge') == '1' else "⏸️  未充电"
                    discharge_status = "⚡ 放电中" if battery_status.get('discharge') == '1' else "⏸️  未放电"
                    print(f"  状态: {charge_status}, {discharge_status}")

            # BMS状态
            bms_status = data['parsed_data'].get('bms_status', {})
            if bms_status:
                print(f"\n🖥️  BMS状态:")
                env_temp = int(bms_status.get('envTemp', 0))
                temp_icon = "🟢" if env_temp <= 40 else "🟡" if env_temp <= 45 else "🔴"
                print(f"  {temp_icon} 环境温度: {env_temp} °C")
                print(f"  🛣️  总里程: {bms_status.get('totalMileage', 'N/A')} km")

                charge_mos = bms_status.get('chargeMosStatus') == 1
                discharge_mos = bms_status.get('disChargeMosStatus') == 1
                mos_icon = "🟢" if charge_mos and discharge_mos else "🟡"
                print(f"  {mos_icon} 充电MOS: {'✅ 开启' if charge_mos else '❌ 关闭'}")
                print(f"  {mos_icon} 放电MOS: {'✅ 开启' if discharge_mos else '❌ 关闭'}")

        # 保护和警告（过滤正常状态）
        protection_warnings = data.get('protection_warnings')
        if protection_warnings:
            alerts = protection_warnings.get('alerts', [])
            protections = protection_warnings.get('protections', [])

            # 过滤掉正常状态的"警告"
            real_alerts = [alert for alert in alerts
                          if not any(normal in alert for normal in ['正常闭合', '正常打开', '定时休眠'])]

            if real_alerts:
                print(f"\n⚠️  警告信息: {', '.join(real_alerts)}")
            if protections:
                print(f"🛡️  保护信息: {', '.join(protections)}")
            if not real_alerts and not protections:
                print(f"\n✅ 状态: 正常，无异常警告")

        print("="*70)

    def get_battery_status_summary(self) -> Dict:
        """获取电池状态摘要"""
        try:
            data = self.get_all_battery_data()
            if not data or not data.get('real_time_data'):
                return {}

            battery_package = data['parsed_data'].get('battery_package', {})
            bms_status = data['parsed_data'].get('bms_status', {})

            # 计算单体电压统计
            cell_voltages = battery_package.get('cellVoltageDetail', [])
            cell_stats = {}
            if cell_voltages:
                voltages = [float(v) for v in cell_voltages]
                cell_stats = {
                    'max_voltage': max(voltages),
                    'min_voltage': min(voltages),
                    'avg_voltage': sum(voltages) / len(voltages),
                    'voltage_diff': max(voltages) - min(voltages),
                    'cell_count': len(voltages)
                }

            # 计算温度统计
            temp_details = battery_package.get('tempDetailInfo', [])
            temp_stats = {}
            if temp_details:
                temps = [float(t) for t in temp_details]
                temp_stats = {
                    'max_temp': max(temps),
                    'min_temp': min(temps),
                    'avg_temp': sum(temps) / len(temps),
                    'temp_diff': max(temps) - min(temps),
                    'sensor_count': len(temps)
                }

            summary = {
                'timestamp': datetime.now().isoformat(),
                'basic_info': {
                    'voltage': float(battery_package.get('totalVoltage', 0)),
                    'current': float(battery_package.get('totalCurrent', 0)),
                    'soc': int(battery_package.get('soc', 0)),
                    'remaining_capacity': float(battery_package.get('residualCapacity', 0)),
                    'current_capacity': float(battery_package.get('currentCapacity', 0)),
                    'cycle_count': int(battery_package.get('loopTimes', 0))
                },
                'cell_stats': cell_stats,
                'temp_stats': temp_stats,
                'bms_info': {
                    'env_temp': int(bms_status.get('envTemp', 0)),
                    'total_mileage': float(bms_status.get('totalMileage', 0)),
                    'charge_mos': bool(bms_status.get('chargeMosStatus', 0)),
                    'discharge_mos': bool(bms_status.get('disChargeMosStatus', 0))
                },
                'health_score': self._calculate_health_score(battery_package, bms_status, cell_stats, temp_stats)
            }

            return summary

        except Exception as e:
            logger.error(f"获取电池状态摘要异常: {e}")
            return {}

    def _calculate_health_score(self, battery_package: Dict, bms_status: Dict,
                               cell_stats: Dict, temp_stats: Dict) -> int:
        """计算电池健康评分 (0-100)"""
        try:
            score = 100

            # SOC评分 (20%)
            soc = int(battery_package.get('soc', 100))
            if soc < 20:
                score -= 20
            elif soc > 95:
                score -= 5

            # 单体电压差异评分 (30%)
            if cell_stats and 'voltage_diff' in cell_stats:
                voltage_diff = cell_stats['voltage_diff']
                if voltage_diff > 0.1:
                    score -= 30
                elif voltage_diff > 0.05:
                    score -= 15

            # 温度评分 (25%)
            if temp_stats and 'max_temp' in temp_stats:
                max_temp = temp_stats['max_temp']
                if max_temp > 45:
                    score -= 25
                elif max_temp > 40:
                    score -= 10
                elif max_temp < 0:
                    score -= 20

            # 循环次数评分 (15%)
            cycle_count = int(battery_package.get('loopTimes', 0))
            if cycle_count > 1000:
                score -= 15
            elif cycle_count > 500:
                score -= 8

            # MOS状态评分 (10%)
            charge_mos = bool(bms_status.get('chargeMosStatus', 0))
            discharge_mos = bool(bms_status.get('disChargeMosStatus', 0))
            if not charge_mos or not discharge_mos:
                score -= 10

            return max(0, min(100, score))

        except Exception as e:
            logger.error(f"计算健康评分异常: {e}")
            return 0

    def monitor_continuous(self, interval: int = 30, duration: int = 3600):
        """连续监控模式

        Args:
            interval: 监控间隔（秒）
            duration: 监控持续时间（秒）
        """
        logger.info(f"开始连续监控，间隔 {interval} 秒，持续 {duration} 秒")

        start_time = time.time()
        while time.time() - start_time < duration:
            try:
                data = self.get_all_battery_data()
                self.print_battery_summary(data)

                # 检查异常状态
                self.check_battery_alerts(data)

                time.sleep(interval)

            except KeyboardInterrupt:
                logger.info("用户中断监控")
                break
            except Exception as e:
                logger.error(f"监控过程中发生异常: {e}")
                time.sleep(interval)

    def check_battery_alerts(self, data: Dict):
        """检查电池异常状态并发出警告"""
        try:
            battery_package = data['parsed_data'].get('battery_package', {})
            alert_count = 0

            if battery_package:
                # 检查电压异常
                total_voltage = float(battery_package.get('totalVoltage', 0))
                if total_voltage > 87.0:  # 过压警告
                    logger.warning(f"⚠️  电池总电压过高: {total_voltage}V")
                    alert_count += 1
                elif total_voltage < 60.0:  # 欠压警告
                    logger.warning(f"⚠️  电池总电压过低: {total_voltage}V")
                    alert_count += 1

                # 检查电流异常
                total_current = float(battery_package.get('totalCurrent', 0))
                if abs(total_current) > 50.0:  # 过流警告
                    logger.warning(f"⚠️  电池电流异常: {total_current}A")
                    alert_count += 1

                # 检查SOC异常
                soc = int(battery_package.get('soc', 100))
                if soc < 20:  # 低电量警告
                    logger.warning(f"🔋 电池电量过低: {soc}%")
                    alert_count += 1
                elif soc > 95:  # 高电量提醒（不算警告）
                    logger.info(f"🔋 电池电量充足: {soc}%")

                # 检查温度异常
                temp_details = battery_package.get('tempDetailInfo', [])
                for i, temp_str in enumerate(temp_details):
                    temp = float(temp_str)
                    if temp > 45.0:  # 高温警告
                        logger.warning(f"🌡️  温度传感器{i+1}温度过高: {temp}°C")
                        alert_count += 1
                    elif temp < -10.0:  # 低温警告
                        logger.warning(f"🌡️  温度传感器{i+1}温度过低: {temp}°C")
                        alert_count += 1

                # 检查单体电压差异
                cell_voltages = battery_package.get('cellVoltageDetail', [])
                if len(cell_voltages) > 1:
                    voltages = [float(v) for v in cell_voltages]
                    max_voltage = max(voltages)
                    min_voltage = min(voltages)
                    voltage_diff = max_voltage - min_voltage

                    if voltage_diff > 0.1:  # 单体电压差异过大
                        logger.warning(f"⚡ 单体电压差异过大: {voltage_diff:.3f}V (最高: {max_voltage}V, 最低: {min_voltage}V)")
                        alert_count += 1

            # 检查真正的保护和警告信息（过滤掉正常状态）
            protection_warnings = data.get('protection_warnings')
            if protection_warnings:
                alerts = protection_warnings.get('alerts', [])
                protections = protection_warnings.get('protections', [])

                # 过滤掉正常状态的"警告"
                real_alerts = []
                for alert in alerts:
                    if not any(normal_status in alert for normal_status in
                             ['正常闭合', '正常打开', '定时休眠', '正常状态']):
                        real_alerts.append(alert)

                for alert in real_alerts:
                    logger.warning(f"🚨 系统警告: {alert}")
                    alert_count += 1

                for protection in protections:
                    logger.error(f"🛡️  系统保护: {protection}")
                    alert_count += 1

            # 如果没有真正的警告，显示正常状态
            if alert_count == 0:
                logger.info("✅ 电池状态正常，无异常警告")

        except Exception as e:
            logger.error(f"检查电池状态异常: {e}")


def main():
    """主程序"""
    print("万洋锂电池监控系统")
    print("基于真实API接口的电池监控和控制工具")
    print("-" * 50)

    # 创建监控器实例
    monitor = WanYangBatteryMonitor()

    while True:
        print("\n请选择操作:")
        print("1. 获取实时电池数据")
        print("2. 获取完整电池信息")
        print("3. 连续监控模式")
        print("4. 获取设备参数")
        print("5. 电池健康状态摘要")
        print("6. 导出数据到JSON文件")
        print("7. 查看电池历史统计")
        print("0. 退出")

        choice = input("\n请输入选择 (0-7): ").strip()

        try:
            if choice == '1':
                print("\n📊 获取实时电池数据...")
                data = monitor.get_real_time_data()
                if data:
                    print(json.dumps(data, indent=2, ensure_ascii=False))
                else:
                    print("❌ 获取实时数据失败")

            elif choice == '2':
                print("\n📋 获取完整电池信息...")
                data = monitor.get_all_battery_data()
                monitor.print_battery_summary(data)

            elif choice == '3':
                interval = input("请输入监控间隔（秒，默认30）: ").strip()
                duration = input("请输入监控持续时间（秒，默认3600）: ").strip()

                interval = int(interval) if interval.isdigit() else 30
                duration = int(duration) if duration.isdigit() else 3600

                print(f"\n🔄 开始连续监控（间隔{interval}秒，持续{duration}秒）...")
                monitor.monitor_continuous(interval, duration)

            elif choice == '4':
                print("\n⚙️  获取设备参数...")
                params = monitor.get_device_parameters()
                if params:
                    print("\n设备保护参数配置:")
                    for key, value in params.items():
                        print(f"  {key}: {value}")
                else:
                    print("❌ 获取设备参数失败")

            elif choice == '5':
                print("\n🏥 电池健康状态摘要...")
                summary = monitor.get_battery_status_summary()
                if summary:
                    print(f"\n电池健康评分: {summary['health_score']}/100")
                    print(f"基本信息: 电压{summary['basic_info']['voltage']}V, SOC{summary['basic_info']['soc']}%")
                    if summary['cell_stats']:
                        print(f"单体电压: 最高{summary['cell_stats']['max_voltage']:.3f}V, 最低{summary['cell_stats']['min_voltage']:.3f}V, 差异{summary['cell_stats']['voltage_diff']:.3f}V")
                    if summary['temp_stats']:
                        print(f"温度状态: 最高{summary['temp_stats']['max_temp']}°C, 最低{summary['temp_stats']['min_temp']}°C")
                else:
                    print("❌ 获取健康状态失败")

            elif choice == '6':
                print("\n💾 导出数据到JSON文件...")
                data = monitor.get_all_battery_data()
                filename = f"battery_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)

                print(f"✅ 数据已导出到: {filename}")

            elif choice == '7':
                print("\n📈 电池历史统计...")
                summary = monitor.get_battery_status_summary()
                if summary:
                    basic = summary['basic_info']
                    print(f"累计循环次数: {basic['cycle_count']} 次")
                    print(f"当前容量: {basic['current_capacity']} Ah")
                    print(f"剩余容量: {basic['remaining_capacity']} Ah")
                    print(f"容量保持率: {(basic['remaining_capacity']/basic['current_capacity']*100):.1f}%")
                else:
                    print("❌ 获取历史统计失败")

            elif choice == '0':
                print("👋 退出程序")
                break

            else:
                print("❌ 无效选择，请重新输入")

        except KeyboardInterrupt:
            print("\n\n用户中断操作")
            break
        except Exception as e:
            print(f"操作异常: {e}")
            logger.error(f"主程序异常: {e}")


if __name__ == "__main__":
    main()

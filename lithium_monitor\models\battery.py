#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from dataclasses import dataclass, field
from typing import List, Dict, Optional
import time
import json

@dataclass
class BatteryModel:
    """电池型号模型"""
    model: str
    voltage: str
    charge_current: str
    discharge_current: str
    size: str
    weight: str
    waterproof: str
    capacity: str = ""
    description: str = ""

@dataclass
class CellVoltage:
    """单体电压模型"""
    cell_number: int
    voltage: float
    timestamp: float = field(default_factory=time.time)

@dataclass
class TemperatureReading:
    """温度读数模型"""
    sensor_number: int
    temperature: float
    timestamp: float = field(default_factory=time.time)

@dataclass
class BatteryStatus:
    """电池状态模型"""
    charge_mos_status: int = 1  # 1=开启, 0=关闭
    discharge_mos_status: int = 1
    dc_mos: int = 1
    charge_mos: int = 1
    
    # 保护状态
    over_voltage_protection: bool = False
    under_voltage_protection: bool = False
    over_current_protection: bool = False
    over_temperature_protection: bool = False
    under_temperature_protection: bool = False
    short_circuit_protection: bool = False
    
    # 警告状态
    voltage_warning: bool = False
    current_warning: bool = False
    temperature_warning: bool = False
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'chargeMOSStatus': str(self.charge_mos_status),
            'disChargeMOSStatus': str(self.discharge_mos_status),
            'DCMOS': str(self.dc_mos),
            'chargeMOS': str(self.charge_mos),
            'protections': {
                'overVoltage': self.over_voltage_protection,
                'underVoltage': self.under_voltage_protection,
                'overCurrent': self.over_current_protection,
                'overTemperature': self.over_temperature_protection,
                'underTemperature': self.under_temperature_protection,
                'shortCircuit': self.short_circuit_protection
            },
            'warnings': {
                'voltage': self.voltage_warning,
                'current': self.current_warning,
                'temperature': self.temperature_warning
            }
        }

@dataclass
class BatteryPackageInfo:
    """电池包信息模型"""
    battery_id: str
    total_voltage: float
    total_current: float
    soc: int  # 电量百分比
    cell_quantity: int
    cell_voltages: List[CellVoltage] = field(default_factory=list)
    temp_quantity: int = 2
    temperatures: List[TemperatureReading] = field(default_factory=list)
    bms_temp: float = 35.0
    residual_capacity: float = 51.30
    current_capacity: float = 53.00
    loop_times: int = 1
    battery_status: BatteryStatus = field(default_factory=BatteryStatus)
    info_time: str = field(default_factory=lambda: str(int(time.time())))
    
    def to_json_string(self) -> str:
        """转换为JSON字符串格式（用于API响应）"""
        data = {
            'infoTime': self.info_time,
            'batteryId': self.battery_id,
            'batteryId26': self.battery_id,
            'totalVoltage': f"{self.total_voltage:.2f}",
            'totalCurrent': f"{self.total_current:.2f}",
            'soc': str(self.soc),
            'cellQuantity': str(self.cell_quantity),
            'cellVoltageDetail': [f"{cv.voltage:.3f}" for cv in self.cell_voltages],
            'tempQuantity': str(self.temp_quantity),
            'tempDetailInfo': [f"{tr.temperature:.2f}" for tr in self.temperatures],
            'BMSTemp': str(int(self.bms_temp)),
            'residualCapacity': f"{self.residual_capacity:.2f}",
            'currentCapacity': f"{self.current_capacity:.2f}",
            'loopTimes': str(self.loop_times),
            'batteryStatus': self.battery_status.to_dict()
        }
        return json.dumps(data, ensure_ascii=False)

@dataclass
class GPSLocation:
    """GPS位置信息模型"""
    latitude: float
    longitude: float
    altitude: float = 0.0
    speed: float = 0.0
    direction: int = 0
    timestamp: str = field(default_factory=lambda: str(int(time.time())))

@dataclass
class BatteryRealtimeData:
    """电池实时数据模型"""
    mobile: str  # 设备手机号
    battery_id: str
    heart_beat_time: str
    location: GPSLocation
    battery_package_info: BatteryPackageInfo
    bms_version: str = "22"
    
    # 设备状态
    acc_status: str = "0"
    is_location: str = "1"
    multi_link: str = "0"
    operate_s: str = "1"
    defend_s: str = "0"
    assist_loc: str = "0"
    shake_s: str = "0"
    add_flag: str = "1"
    
    def to_dict(self) -> Dict:
        """转换为字典格式（用于API响应）"""
        return {
            'mobile': self.mobile,
            'heartBeatTime': self.heart_beat_time,
            'latitude': str(self.location.latitude),
            'longitude': str(self.location.longitude),
            'altitude': str(self.location.altitude),
            'speed': str(self.location.speed),
            'direction': str(self.location.direction),
            'time': self.location.timestamp,
            'accStatus': self.acc_status,
            'isLocation': self.is_location,
            'multiLink': self.multi_link,
            'operateS': self.operate_s,
            'defendS': self.defend_s,
            'assistLoc': self.assist_loc,
            'shakeS': self.shake_s,
            'addFlag': self.add_flag,
            'batteryId': self.battery_id,
            'batteryId26': self.battery_id,
            'bmsVersion': self.bms_version,
            'batteryPackageInfo': self.battery_package_info.to_json_string()
        }

@dataclass
class UserBattery:
    """用户电池关联模型"""
    id: str
    user_id: str
    client_id: str
    battery_code: str
    battery_type: str
    name: str
    owner: str
    create_time: str = field(default_factory=lambda: time.strftime('%Y-%m-%d %H:%M:%S'))
    update_time: Optional[str] = None
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'id': self.id,
            'userId': self.user_id,
            'clientId': self.client_id,
            'batteryCode': self.battery_code,
            'batteryType': self.battery_type,
            'name': self.name,
            'owner': self.owner,
            'createTime': self.create_time,
            'updateTime': self.update_time
        }

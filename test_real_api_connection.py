#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
from typing import Dict, Any

class RealAPITester:
    """测试万洋锂电真实API连接"""
    
    def __init__(self):
        self.wanyang_server = "https://sys.wyzxcn.com/jeecg-boot"
        self.local_server = "http://127.0.0.1:5000"
        
        # 从XML抓包中提取的认证信息
        self.auth_configs = [
            {
                'name': '配置1 - 从XML抓包',
                'key': '79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c',
                'clientId': '380074209785'
            },
            {
                'name': '配置2 - 备用',
                'key': 'test_key',
                'clientId': 'test_client'
            }
        ]
        
    def test_all_connections(self):
        """测试所有连接"""
        print("🔗 测试万洋锂电真实API连接...")
        print("=" * 80)
        
        # 测试本地服务器
        self.test_local_server()
        
        # 测试万洋锂电服务器直连
        self.test_wanyang_direct()
        
        # 测试通过本地代理
        self.test_local_proxy()
        
        # 生成解决方案
        self.generate_solutions()
    
    def test_local_server(self):
        """测试本地服务器"""
        print("\n🏠 测试本地服务器连接...")
        
        try:
            response = requests.get(f"{self.local_server}/health", timeout=5)
            if response.status_code == 200:
                print("  ✅ 本地服务器连接正常")
            else:
                print(f"  ❌ 本地服务器响应异常: {response.status_code}")
        except Exception as e:
            print(f"  ❌ 本地服务器连接失败: {e}")
    
    def test_wanyang_direct(self):
        """测试万洋锂电服务器直连"""
        print("\n🌐 测试万洋锂电服务器直连...")
        
        for config in self.auth_configs:
            print(f"\n  📋 测试 {config['name']}:")
            print(f"    Key: {config['key'][:20]}...")
            print(f"    ClientId: {config['clientId']}")
            
            # 测试实时数据接口
            self.test_wanyang_endpoint(
                '/fnjbattery/realTime',
                config,
                '实时数据'
            )
            
            # 测试控制接口
            self.test_wanyang_endpoint(
                '/fnjbattery/terminalControl',
                {**config, 'cmdId': '40'},
                '控制命令'
            )
    
    def test_wanyang_endpoint(self, endpoint: str, params: Dict[str, str], description: str):
        """测试万洋锂电单个接口"""
        try:
            url = f"{self.wanyang_server}{endpoint}"
            
            print(f"    🔗 测试{description}: {endpoint}")
            
            response = requests.get(url, params=params, timeout=10)
            
            print(f"    📊 响应状态: {response.status_code}")
            
            if response.status_code == 200:
                print(f"    ✅ {description}接口连接成功")
                
                # 尝试解析响应
                try:
                    data = response.json()
                    print(f"    📄 响应格式: JSON")
                    if isinstance(data, dict):
                        print(f"    🔑 响应字段: {list(data.keys())[:5]}...")
                except:
                    print(f"    📄 响应格式: 文本 ({len(response.text)} 字符)")
                    
            elif response.status_code == 401:
                print(f"    🔐 {description}接口需要认证 (401)")
                print(f"    💡 可能需要有效的认证信息")
                
            elif response.status_code == 403:
                print(f"    🚫 {description}接口访问被禁止 (403)")
                print(f"    💡 可能需要特定的权限或IP白名单")
                
            elif response.status_code == 404:
                print(f"    ❓ {description}接口不存在 (404)")
                print(f"    💡 接口路径可能已变更")
                
            else:
                print(f"    ❌ {description}接口响应异常: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"    ⏰ {description}接口请求超时")
            
        except requests.exceptions.ConnectionError:
            print(f"    🔌 {description}接口连接失败")
            
        except Exception as e:
            print(f"    ❌ {description}接口测试异常: {e}")
    
    def test_local_proxy(self):
        """测试本地代理"""
        print("\n🔄 测试本地API代理...")
        
        # 测试代理接口
        proxy_endpoints = [
            '/api/real/realtime',
            '/api/real/test-connection'
        ]
        
        for endpoint in proxy_endpoints:
            try:
                url = f"{self.local_server}{endpoint}"
                print(f"  🔗 测试代理接口: {endpoint}")
                
                response = requests.get(url, timeout=10)
                
                print(f"  📊 代理响应状态: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"  ✅ 代理接口正常")
                else:
                    print(f"  ❌ 代理接口异常: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ 代理接口测试失败: {e}")
    
    def generate_solutions(self):
        """生成解决方案"""
        print("\n" + "=" * 80)
        print("💡 万洋锂电API连接解决方案")
        print("=" * 80)
        
        print("\n🔍 问题分析:")
        issues = [
            "1. 万洋锂电服务器返回401认证错误",
            "2. 从XML抓包的认证信息可能已过期",
            "3. 服务器可能需要特定的请求头或签名",
            "4. 可能需要IP白名单或特殊权限"
        ]
        
        for issue in issues:
            print(f"  {issue}")
        
        print("\n🚀 解决方案:")
        solutions = [
            "方案1: 获取有效的认证信息",
            "  • 联系万洋锂电获取有效的API密钥",
            "  • 确认设备ID和客户端ID的正确性",
            "  • 检查认证信息是否有时效性",
            "",
            "方案2: 分析完整的请求格式",
            "  • 深入分析XML抓包中的完整HTTP请求",
            "  • 提取所有请求头信息",
            "  • 分析是否需要特殊的签名算法",
            "",
            "方案3: 实现本地数据模拟",
            "  • 基于真实数据格式创建高仿真模拟器",
            "  • 实现数据的动态变化和状态同步",
            "  • 提供完整的控制功能模拟",
            "",
            "方案4: 混合模式实现",
            "  • 在有网络连接时尝试获取真实数据",
            "  • 在无法连接时使用本地模拟数据",
            "  • 提供数据源切换功能"
        ]
        
        for solution in solutions:
            print(f"  {solution}")
        
        print("\n🎯 推荐实施步骤:")
        steps = [
            "1. 立即实现方案3 - 创建高仿真本地模拟器",
            "2. 同时进行方案2 - 深入分析XML抓包数据",
            "3. 联系万洋锂电技术支持获取API文档",
            "4. 实现方案4 - 混合模式，提供最佳用户体验"
        ]
        
        for step in steps:
            print(f"  {step}")
        
        print("\n✅ 当前可行的实现:")
        current_options = [
            "• 基于真实数据格式的高仿真模拟器",
            "• 完整的Web监控界面",
            "• 功能完善的MOS控制面板",
            "• 真实的数据结构和字段",
            "• 动态数据变化模拟",
            "• 完整的用户交互体验"
        ]
        
        for option in current_options:
            print(f"  {option}")

def main():
    tester = RealAPITester()
    tester.test_all_connections()

if __name__ == "__main__":
    main()

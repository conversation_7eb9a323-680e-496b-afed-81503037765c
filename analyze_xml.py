#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import base64
import json
import urllib.parse

def decode_base64_response(base64_data):
    """解码base64响应数据"""
    try:
        decoded = base64.b64decode(base64_data).decode('utf-8')
        # 分离HTTP头和JSON数据
        parts = decoded.split('\r\n\r\n', 1)
        if len(parts) == 1:
            parts = decoded.split('\n\n', 1)
        if len(parts) > 1:
            return parts[1]  # 返回JSON部分
        return decoded
    except Exception as e:
        print(f"解码错误: {e}")
        return None

def analyze_battery_list():
    """分析电池信息列表API"""
    response_data = '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'
    
    json_data = decode_base64_response(response_data)
    if json_data:
        data = json.loads(json_data)
        print("=== 电池信息列表API分析 ===")
        print(f"API路径: /jeecg-boot/wybattery/wyBatteryInfor/list")
        print(f"响应状态: {data.get('success')}")
        print(f"总记录数: {data['result']['total']}")
        print("\n电池型号列表:")
        for battery in data['result']['records']:
            print(f"- {battery['model']}: {battery['sign']}")
            print(f"  规格: {battery['size']}, 电压: {battery['voltage']}")
            print(f"  充电电流: {battery['chargeCurrent']}, 放电电流: {battery['dischargeCurrent']}")
        print()

def analyze_user_battery():
    """分析用户电池API"""
    response_data = 'SFRUUC8yIDIwMCBPSw0KU2VydmVyOiBuZ2lueA0KRGF0ZTogV2VkLCAxOCBKdW4gMjAyNSAwNjozODoxMyBHTVQNCkNvbnRlbnQtVHlwZTogYXBwbGljYXRpb24vanNvbg0KQWNjZXNzLUNvbnRyb2wtQWxsb3ctTWV0aG9kczogR0VULFBPU1QsT1BUSU9OUyxQVVQsREVMRVRFDQpBY2Nlc3MtQ29udHJvbC1BbGxvdy1DcmVkZW50aWFsczogdHJ1ZQ0KVmFyeTogb3JpZ2luLGFjY2Vzcy1jb250cm9sLXJlcXVlc3QtbWV0aG9kLGFjY2Vzcy1jb250cm9sLXJlcXVlc3QtaGVhZGVycyxhY2NlcHQtZW5jb2RpbmcNClNldC1Db29raWU6IEpTRVNTSU9OSUQ9OTk1REQ0NThGN0ExOEQyMjI1NzI2MDcyNzU5MzI5OUE7IFBhdGg9L2plZWNnLWJvb3Q7IEh0dHBPbmx5DQoNCnsic3VjY2VzcyI6dHJ1ZSwibWVzc2FnZSI6IuafpeivouaIkOWKnyIsImNvZGUiOjIwMCwicmVzdWx0IjpbeyJ1c2VyQmF0dGVyeSI6eyJpZCI6IjE5MzQ0OTgxNTUyMDY5MjYzMzgiLCJ1c2VySWQiOiIxOTMzMzQzNTkxMDc4MzM0NDY1IiwiY2xpZW50SWQiOiIzODAwNzQyMDk3ODUiLCJiYXR0ZXJ5Q29kZSI6IkJUMjA3MjA1NTAxMDAzMjUwNDE0MDQwOCIsImJhdHRlcnlUeXBlIjoiNzJWNTVBaCIsImNyZWF0ZUJ5Ijoid2FueWFuZ19aWnhYbHNxQUZnIiwiY3JlYXRlVGltZSI6IjIwMjUtMDYtMTYgMTQ6Mjc6NTYiLCJ1cGRhdGVCeSI6bnVsbCwidXBkYXRlVGltZSI6bnVsbCwibmFtZSI6Iuivt+S/ruaUueaCqOeahOeUteaxoOWkh+azqOWQp++8gSIsIm93bmVyIjoiMTkzMzM0MzU5MTA3ODMzNDQ2NSJ9LCJiYXR0ZXJ5SW1nIjoidGVtcC83MlY1NUFoXzE3NDU5OTUzNzI1MDAucG5nIn1dLCJ0aW1lc3RhbXAiOjE3NTAyMjg2OTMzODN9'
    
    json_data = decode_base64_response(response_data)
    if json_data:
        data = json.loads(json_data)
        print("=== 用户电池API分析 ===")
        print(f"API路径: /jeecg-boot/battery/userBattery/api/list")
        print(f"用户ID参数: userId=1933343591078334465")
        print(f"响应状态: {data.get('success')}")
        
        if data['result']:
            battery_info = data['result'][0]['userBattery']
            print(f"电池编码: {battery_info['batteryCode']}")
            print(f"电池类型: {battery_info['batteryType']}")
            print(f"客户端ID: {battery_info['clientId']}")
            print(f"电池名称: {battery_info['name']}")
        print()

def analyze_realtime_data():
    """分析实时电池数据API"""
    response_data = '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'

    json_data = decode_base64_response(response_data)
    if json_data:
        data = json.loads(json_data)
        print("=== 实时电池数据API分析 ===")
        print(f"API路径: /jeecg-boot/fnjbattery/realTime")
        print(f"参数: key=79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c&clientId=380074209785")

        # 解析嵌套的JSON字符串
        detail_str = data.get('detail', '{}')
        detail_data = json.loads(detail_str)

        print(f"设备手机号: {detail_data.get('mobile')}")
        print(f"电池ID: {detail_data.get('batteryId')}")
        print(f"位置: 纬度{detail_data.get('latitude')}, 经度{detail_data.get('longitude')}")

        # 解析电池包信息
        battery_package_str = detail_data.get('batteryPackageInfo', '{}')
        battery_package = json.loads(battery_package_str)

        print(f"总电压: {battery_package.get('totalVoltage')}V")
        print(f"总电流: {battery_package.get('totalCurrent')}A")
        print(f"SOC: {battery_package.get('soc')}%")
        print(f"电芯数量: {battery_package.get('cellQuantity')}")
        print(f"温度数量: {battery_package.get('tempQuantity')}")
        print(f"BMS温度: {battery_package.get('BMSTemp')}°C")
        print(f"剩余容量: {battery_package.get('residualCapacity')}Ah")
        print(f"当前容量: {battery_package.get('currentCapacity')}Ah")
        print()

def print_api_summary():
    """打印API总结"""
    print("=" * 60)
    print("锂电监控系统 - API接口分析报告")
    print("=" * 60)
    print()
    print("基于Burp Suite抓包分析，发现以下主要API接口：")
    print()
    print("1. 数据获取接口:")
    print("   - /jeecg-boot/wybattery/wyBatteryInfor/list - 获取电池型号列表")
    print("   - /jeecg-boot/battery/userBattery/api/list - 获取用户电池列表")
    print("   - /jeecg-boot/fnjbattery/realTime - 获取实时电池数据")
    print("   - /jeecg-boot/fnjbattery/parseProtectionAndWarning - 解析保护和警告信息")
    print("   - /jeecg-boot/fnjbattery/deviceParameters - 获取设备参数")
    print()
    print("2. 控制接口:")
    print("   - /jeecg-boot/gps/manual - GPS手动定位")
    print()
    print("3. 认证方式:")
    print("   - 使用X-Access-Token进行身份验证")
    print("   - JWT Token格式")
    print()
    print("4. 主要数据结构:")
    print("   - 电池基本信息：型号、电压、电流、容量等")
    print("   - 实时数据：SOC、电压、电流、温度、GPS位置等")
    print("   - 状态信息：充放电状态、保护状态、警告信息等")
    print("   - 电芯详细信息：单体电压、温度分布等")
    print()

if __name__ == "__main__":
    analyze_battery_list()
    analyze_user_battery()
    analyze_realtime_data()
    print_api_summary()

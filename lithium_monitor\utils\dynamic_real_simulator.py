#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import random
import json
import math
from typing import Dict, List, Any
from datetime import datetime

class DynamicRealDataSimulator:
    """动态真实数据模拟器 - 生成真正变化的实时数据"""
    
    def __init__(self, client_id: str = "380074209785", battery_id: str = "BT2072055010032504140408"):
        self.client_id = client_id
        self.battery_id = battery_id
        self.start_time = time.time()
        
        # 动态数据状态
        self.current_state = {
            'soc': 96.0,                    # 当前SOC
            'total_voltage': 84.40,         # 总电压
            'total_current': 0.0,           # 总电流
            'bms_temp': 35.0,               # BMS温度
            'env_temp': 36.0,               # 环境温度
            'charge_mos': 1,                # 充电MOS状态
            'discharge_mos': 1,             # 放电MOS状态
            'is_charging': False,           # 是否正在充电
            'is_discharging': False,        # 是否正在放电
            'last_control_time': 0,         # 最后控制时间
            'cycle_count': 0,               # 循环计数
            'total_mileage': 18.2,          # 总里程
            'residual_capacity': 51.30,     # 剩余容量
            'current_capacity': 53.00       # 当前容量
        }
        
        # 基础单体电压（会动态变化）
        self.base_cell_voltages = [
            4.222, 4.223, 4.223, 4.224, 4.223,
            4.221, 4.224, 4.225, 4.223, 4.225,
            4.220, 4.223, 4.224, 4.222, 4.223,
            4.224, 4.223, 4.223, 4.223, 4.222
        ]
        
        # 基础温度（会动态变化）
        self.base_temperatures = [34.0, 33.0]
        
        # 模拟参数
        self.simulation_params = {
            'voltage_variation': 0.005,      # 电压变化幅度
            'current_variation': 0.2,        # 电流变化幅度
            'temp_variation': 0.5,           # 温度变化幅度
            'soc_change_rate': 0.01,         # SOC变化率
            'charge_current_range': (5.0, 15.0),    # 充电电流范围
            'discharge_current_range': (-20.0, -5.0), # 放电电流范围
        }
        
    def update_dynamic_state(self):
        """更新动态状态"""
        current_time = time.time()
        elapsed = current_time - self.start_time
        
        # 模拟充放电状态变化
        self._simulate_charge_discharge_cycle(elapsed)
        
        # 更新电压
        self._update_voltage()
        
        # 更新电流
        self._update_current()
        
        # 更新温度
        self._update_temperature()
        
        # 更新SOC
        self._update_soc()
        
        # 更新其他参数
        self._update_other_params()
        
    def _simulate_charge_discharge_cycle(self, elapsed: float):
        """模拟充放电循环"""
        # 每5分钟一个周期
        cycle_duration = 300  # 5分钟
        cycle_position = (elapsed % cycle_duration) / cycle_duration
        
        # 只有在MOS开启时才能充放电
        can_charge = self.current_state['charge_mos'] == 1
        can_discharge = self.current_state['discharge_mos'] == 1
        
        if cycle_position < 0.3 and can_charge:
            # 充电阶段 (30%)
            self.current_state['is_charging'] = True
            self.current_state['is_discharging'] = False
        elif cycle_position < 0.6 and can_discharge:
            # 放电阶段 (30%)
            self.current_state['is_charging'] = False
            self.current_state['is_discharging'] = True
        else:
            # 静置阶段 (40%)
            self.current_state['is_charging'] = False
            self.current_state['is_discharging'] = False
    
    def _update_voltage(self):
        """更新电压"""
        base_voltage = 84.40
        
        # 根据SOC调整电压
        soc_factor = self.current_state['soc'] / 100.0
        voltage_adjustment = (soc_factor - 0.5) * 2.0  # ±1V
        
        # 添加随机波动
        random_variation = random.uniform(-0.02, 0.02)
        
        self.current_state['total_voltage'] = base_voltage + voltage_adjustment + random_variation
        
        # 更新单体电压
        for i in range(len(self.base_cell_voltages)):
            base = self.base_cell_voltages[i]
            variation = random.uniform(-0.003, 0.003)
            soc_adj = voltage_adjustment / 20  # 分配到单体
            self.base_cell_voltages[i] = max(3.0, min(4.3, base + soc_adj + variation))
    
    def _update_current(self):
        """更新电流"""
        if self.current_state['is_charging']:
            # 充电电流 (正值)
            min_current, max_current = self.simulation_params['charge_current_range']
            base_current = random.uniform(min_current, max_current)
            variation = random.uniform(-0.5, 0.5)
            self.current_state['total_current'] = base_current + variation
            
        elif self.current_state['is_discharging']:
            # 放电电流 (负值)
            min_current, max_current = self.simulation_params['discharge_current_range']
            base_current = random.uniform(min_current, max_current)
            variation = random.uniform(-0.5, 0.5)
            self.current_state['total_current'] = base_current + variation
            
        else:
            # 静置状态 (接近0)
            self.current_state['total_current'] = random.uniform(-0.1, 0.1)
    
    def _update_temperature(self):
        """更新温度"""
        # BMS温度受电流影响
        current_abs = abs(self.current_state['total_current'])
        temp_increase = current_abs * 0.1  # 电流越大温度越高
        
        base_bms_temp = 35.0
        self.current_state['bms_temp'] = base_bms_temp + temp_increase + random.uniform(-0.5, 0.5)
        
        # 环境温度缓慢变化
        self.current_state['env_temp'] = 36.0 + random.uniform(-1.0, 1.0)
        
        # 更新传感器温度
        for i in range(len(self.base_temperatures)):
            base = 34.0 if i == 0 else 33.0
            self.base_temperatures[i] = base + temp_increase * 0.5 + random.uniform(-0.5, 0.5)
    
    def _update_soc(self):
        """更新SOC"""
        if self.current_state['is_charging']:
            # 充电时SOC增加
            soc_increase = abs(self.current_state['total_current']) * 0.001  # 0.1%/A
            self.current_state['soc'] = min(100.0, self.current_state['soc'] + soc_increase)
            
        elif self.current_state['is_discharging']:
            # 放电时SOC减少
            soc_decrease = abs(self.current_state['total_current']) * 0.001
            self.current_state['soc'] = max(0.0, self.current_state['soc'] - soc_decrease)
    
    def _update_other_params(self):
        """更新其他参数"""
        # 剩余容量根据SOC计算
        max_capacity = 55.0
        self.current_state['residual_capacity'] = (self.current_state['soc'] / 100.0) * max_capacity
        
        # 当前容量微小变化
        self.current_state['current_capacity'] = 53.0 + random.uniform(-0.1, 0.1)
        
        # 里程缓慢增加
        if self.current_state['is_discharging']:
            self.current_state['total_mileage'] += random.uniform(0.001, 0.005)
    
    def control_mos(self, cmd_id: str) -> Dict[str, Any]:
        """控制MOS开关"""
        print(f"🎛️ 执行MOS控制命令: {cmd_id}")
        
        old_charge = self.current_state['charge_mos']
        old_discharge = self.current_state['discharge_mos']
        
        if cmd_id == '40':  # 关闭放电MOS
            self.current_state['discharge_mos'] = 0
            self.current_state['is_discharging'] = False
            
        elif cmd_id == '41':  # 开启放电MOS
            self.current_state['discharge_mos'] = 1
            
        elif cmd_id == '08':  # 开启充/放电MOS（同时）
            self.current_state['charge_mos'] = 1
            self.current_state['discharge_mos'] = 1
            
        elif cmd_id == '09':  # 关闭充/放电MOS（同时）
            self.current_state['charge_mos'] = 0
            self.current_state['discharge_mos'] = 0
            self.current_state['is_charging'] = False
            self.current_state['is_discharging'] = False
        
        self.current_state['last_control_time'] = time.time()
        
        # 返回控制结果
        return {
            'success': True,
            'command_id': cmd_id,
            'old_state': {'charge': old_charge, 'discharge': old_discharge},
            'new_state': {
                'charge': self.current_state['charge_mos'],
                'discharge': self.current_state['discharge_mos']
            },
            'timestamp': int(time.time() * 1000)
        }
    
    def generate_realtime_data(self) -> Dict[str, Any]:
        """生成动态实时数据"""
        # 更新动态状态
        self.update_dynamic_state()
        
        current_time = self.get_current_time_string()
        
        # 生成电池包信息
        battery_package_info = {
            'infoTime': current_time,
            'batteryId': self.battery_id,
            'batteryId26': self.battery_id,
            'totalVoltage': f"{self.current_state['total_voltage']:.2f}",
            'totalCurrent': f"{self.current_state['total_current']:.2f}",
            'soc': str(int(self.current_state['soc'])),
            'cellQuantity': '20',
            'cellVoltageDetail': [f"{v:.3f}" for v in self.base_cell_voltages],
            'tempQuantity': '2',
            'tempDetailInfo': [f"{t:.2f}" for t in self.base_temperatures],
            'BMSTemp': str(int(self.current_state['bms_temp'])),
            'residualCapacity': f"{self.current_state['residual_capacity']:.2f}",
            'currentCapacity': f"{self.current_state['current_capacity']:.2f}",
            'batteryStatus': {
                'chargeMOSStatus': str(self.current_state['charge_mos']),
                'disChargeMOSStatus': str(self.current_state['discharge_mos']),
                'DCMOS': str(self.current_state['discharge_mos']),
                'chargeMOS': str(self.current_state['charge_mos']),
                # 其他状态位保持默认
                'eDetectOpen': '0', 'dischargeOverCurrent': '0', 'eCoreTempHigh': '0',
                'tempDetectOpenCircuit': '0', 'rent': '0', 'BMSPowerDown': '0',
                'chargeOverCurrent': '0', 'validBCode': '0', 'eCoreTempUnder': '0',
                'BMSTempOver': '0', 'BMSFailureStatus': '0', 'discharge': '0',
                'eCoreTempOver': '0', 'BMSTempHigh': '0', 'eDetectOpenCircuit': '0',
                'eCoreOverVol': '0', 'charge': '0', 'BMSStandMode': '0',
                'BMSPowerDownMode': '0', 'cOverCur': '0', 'forbidDCDuration': '0',
                'eCoreTempLow': '0', 'shortCut': '0', 'forbidDischarge': '0',
                'fillUp': '0', 'dcOverCur': '0', 'forbidCharge': '0',
                'eCoreUnderVol': '0', 'BMSFailure': '0', 'forbidDC': '0',
                'BMSStand': '0', 'permitDCDuration': '0', 'tempDetectOpen': '0', 'dc': '0'
            }
        }
        
        # 生成BMS状态信息
        bms_status_info = {
            'infoTime': current_time,
            'envTemp': str(int(self.current_state['env_temp'])),
            'alertStatusBit': '0000000000000000',
            'protectStatusBit': '0000000000000000',
            'chargeMosStatus': self.current_state['charge_mos'],
            'disChargeMosStatus': self.current_state['discharge_mos'],
            'totalMileage': f"{self.current_state['total_mileage']:.1f}",
            'volDiffer': f"{max(self.base_cell_voltages) - min(self.base_cell_voltages):.3f}",
            'averageVol': f"{sum(self.base_cell_voltages) / len(self.base_cell_voltages):.3f}",
            'balanceStatus1Bit': '0000110110000111',
            'balanceStatus2Bit': '0000000010101000'
        }
        
        # 生成完整的实时数据
        realtime_data = {
            'mobile': self.client_id,
            'heartBeatTime': current_time,
            'latitude': '22.506839',
            'longitude': '113.417564',
            'altitude': '3',
            'speed': '0',
            'direction': '164',
            'time': current_time,
            'batteryId': self.battery_id,
            'batteryId26': self.battery_id,
            'bmsVersion': '22',
            'batteryPackageInfo': json.dumps(battery_package_info, ensure_ascii=False),
            'bmsStatusInfo': json.dumps(bms_status_info, ensure_ascii=False),
            'baseStationInfo': json.dumps({
                'GSMSignalIntensity': '20',
                'locationSatNum': '8',
                'CELLID': '12345',
                'DBM': '-65'
            }, ensure_ascii=False)
        }
        
        return realtime_data
    
    def get_current_time_string(self) -> str:
        """获取当前时间字符串"""
        now = datetime.now()
        return now.strftime("%y%m%d%H%M%S")

# 全局动态模拟器实例
dynamic_simulator = DynamicRealDataSimulator()

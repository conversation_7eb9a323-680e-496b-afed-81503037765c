#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import random
import time
import math
from typing import Dict, List
from models.battery import (
    BatteryRealtimeData, BatteryPackageInfo, BatteryStatus,
    CellVoltage, TemperatureReading, GPSLocation
)
from models.device import Device, BMSStatusInfo, BaseStationInfo

class BatteryDataSimulator:
    """电池数据模拟器"""
    
    def __init__(self, client_id: str = "380074209785", battery_id: str = "BT2072055010032504140408"):
        self.client_id = client_id
        self.battery_id = battery_id
        self.base_voltage = 4.2  # 基础单体电压
        self.base_soc = 96  # 基础SOC
        self.base_temperature = 35.0  # 基础温度
        self.base_latitude = 22.506839  # 基础纬度
        self.base_longitude = 113.417564  # 基础经度
        
        # 模拟变化范围
        self.voltage_variation = 0.05  # 电压变化范围
        self.soc_variation = 5  # SOC变化范围
        self.temp_variation = 3.0  # 温度变化范围
        self.location_variation = 0.001  # 位置变化范围
        
        # 时间相关
        self.start_time = time.time()
        
    def generate_cell_voltages(self, count: int = 20) -> List[CellVoltage]:
        """生成单体电压数据"""
        voltages = []
        for i in range(count):
            # 添加小幅随机变化
            voltage = self.base_voltage + random.uniform(-self.voltage_variation, self.voltage_variation)
            voltages.append(CellVoltage(
                cell_number=i + 1,
                voltage=round(voltage, 3)
            ))
        return voltages
    
    def generate_temperatures(self, count: int = 2) -> List[TemperatureReading]:
        """生成温度数据"""
        temperatures = []
        for i in range(count):
            temp = self.base_temperature + random.uniform(-self.temp_variation, self.temp_variation)
            temperatures.append(TemperatureReading(
                sensor_number=i + 1,
                temperature=round(temp, 2)
            ))
        return temperatures
    
    def generate_gps_location(self) -> GPSLocation:
        """生成GPS位置数据"""
        # 模拟轻微的位置变化
        lat_offset = random.uniform(-self.location_variation, self.location_variation)
        lon_offset = random.uniform(-self.location_variation, self.location_variation)
        
        return GPSLocation(
            latitude=self.base_latitude + lat_offset,
            longitude=self.base_longitude + lon_offset,
            altitude=random.uniform(0, 10),
            speed=random.uniform(0, 5),
            direction=random.randint(0, 360)
        )
    
    def generate_battery_status(self) -> BatteryStatus:
        """生成电池状态"""
        # 从控制API获取当前MOS状态
        try:
            from api.control import current_mos_status
            charge_mos = current_mos_status['chargeMOS']
            discharge_mos = current_mos_status['dischargeMOS']
        except:
            charge_mos = 1
            discharge_mos = 1
        
        return BatteryStatus(
            charge_mos_status=charge_mos,
            discharge_mos_status=discharge_mos,
            dc_mos=1,
            charge_mos=charge_mos,
            # 随机生成一些保护状态
            over_voltage_protection=random.random() < 0.05,
            under_voltage_protection=random.random() < 0.05,
            over_current_protection=random.random() < 0.05,
            over_temperature_protection=random.random() < 0.05
        )
    
    def generate_soc(self) -> int:
        """生成SOC数据"""
        # 模拟SOC的缓慢变化
        elapsed_time = time.time() - self.start_time
        # 每小时变化1%
        soc_change = int(elapsed_time / 3600)
        current_soc = max(10, min(100, self.base_soc - soc_change + random.randint(-2, 2)))
        return current_soc
    
    def generate_total_voltage(self, cell_voltages: List[CellVoltage]) -> float:
        """根据单体电压计算总电压"""
        total = sum(cv.voltage for cv in cell_voltages)
        return round(total, 2)
    
    def generate_current(self) -> float:
        """生成电流数据"""
        # 模拟充放电电流
        current_patterns = [0.0, 0.0, 0.0, 5.2, -3.8, 8.1, -12.5, 0.0]
        return random.choice(current_patterns)
    
    def generate_battery_package_info(self) -> BatteryPackageInfo:
        """生成电池包信息"""
        cell_voltages = self.generate_cell_voltages(20)
        temperatures = self.generate_temperatures(2)
        total_voltage = self.generate_total_voltage(cell_voltages)
        current = self.generate_current()
        soc = self.generate_soc()
        
        return BatteryPackageInfo(
            battery_id=self.battery_id,
            total_voltage=total_voltage,
            total_current=current,
            soc=soc,
            cell_quantity=20,
            cell_voltages=cell_voltages,
            temp_quantity=2,
            temperatures=temperatures,
            bms_temp=self.base_temperature + random.uniform(-2, 2),
            residual_capacity=51.30 + random.uniform(-5, 5),
            current_capacity=53.00 + random.uniform(-3, 3),
            battery_status=self.generate_battery_status()
        )
    
    def generate_realtime_data(self) -> Dict:
        """生成完整的实时数据"""
        location = self.generate_gps_location()
        battery_package = self.generate_battery_package_info()
        
        realtime_data = BatteryRealtimeData(
            mobile=self.client_id,
            battery_id=self.battery_id,
            heart_beat_time=str(int(time.time())),
            location=location,
            battery_package_info=battery_package
        )
        
        return realtime_data.to_dict()
    
    def generate_historical_data(self, hours: int = 24, interval_minutes: int = 5) -> List[Dict]:
        """生成历史数据"""
        historical_data = []
        current_time = time.time()
        
        # 计算数据点数量
        total_points = (hours * 60) // interval_minutes
        
        for i in range(total_points):
            # 计算时间点
            timestamp = current_time - (total_points - i) * interval_minutes * 60
            
            # 临时修改基础值来模拟历史变化
            original_soc = self.base_soc
            original_temp = self.base_temperature
            
            # 模拟历史变化
            self.base_soc = max(20, min(100, original_soc + random.randint(-10, 5)))
            self.base_temperature = original_temp + random.uniform(-5, 5)
            
            # 生成数据点
            data_point = self.generate_realtime_data()
            data_point['timestamp'] = timestamp
            historical_data.append(data_point)
            
            # 恢复原始值
            self.base_soc = original_soc
            self.base_temperature = original_temp
        
        return historical_data

class AlertSimulator:
    """警报模拟器"""
    
    def __init__(self):
        self.alert_types = [
            '电压过高警告',
            '电压过低警告', 
            '温度过高警告',
            '电流过大警告',
            '充电MOS异常',
            '放电MOS异常',
            '通信异常',
            'BMS故障'
        ]
    
    def generate_random_alerts(self, count: int = None) -> List[str]:
        """生成随机警报"""
        if count is None:
            count = random.randint(0, 3)
        
        if count == 0:
            return []
        
        return random.sample(self.alert_types, min(count, len(self.alert_types)))
    
    def generate_protection_status(self) -> List[str]:
        """生成保护状态"""
        protections = []
        
        # 根据概率生成保护状态
        if random.random() < 0.1:  # 10%概率
            protections.extend(['过压保护激活', '充电禁止'])
        
        if random.random() < 0.05:  # 5%概率
            protections.extend(['过流保护激活', '放电禁止'])
        
        return protections

class DeviceSimulator:
    """设备模拟器"""
    
    def __init__(self, client_id: str = "380074209785"):
        self.client_id = client_id
        self.devices = {}
        self.create_default_device()
    
    def create_default_device(self):
        """创建默认设备"""
        device = Device(
            client_id=self.client_id,
            mobile=self.client_id,
            battery_id="BT2072055010032504140408",
            user_id="1933343591078334465",
            device_key="79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c"
        )
        self.devices[self.client_id] = device
    
    def get_device(self, client_id: str) -> Device:
        """获取设备"""
        if client_id not in self.devices:
            self.create_default_device()
        return self.devices[client_id]
    
    def update_device_status(self, client_id: str, **kwargs):
        """更新设备状态"""
        device = self.get_device(client_id)
        device.update_heartbeat()
        
        # 更新BMS状态
        if 'charge_mos' in kwargs or 'discharge_mos' in kwargs:
            device.bms_status_info.charge_mos_status = kwargs.get('charge_mos', 1)
            device.bms_status_info.dis_charge_mos_status = kwargs.get('discharge_mos', 1)

# 全局模拟器实例
battery_simulator = BatteryDataSimulator()
alert_simulator = AlertSimulator()
device_simulator = DeviceSimulator()

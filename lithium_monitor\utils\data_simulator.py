#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import random
import time
import json
from typing import Dict, List, Any
import urllib.parse

class RealBatteryDataSimulator:
    """基于真实XML数据的电池数据模拟器"""

    def __init__(self, client_id: str = "380074209785", battery_id: str = "BT2072055010032504140408"):
        self.client_id = client_id
        self.battery_id = battery_id

        # 基于真实XML抓包数据的精确值
        self.base_values = {
            'total_voltage': 84.40,      # 真实值：84.40V
            'total_current': 0.00,       # 真实值：0.00A（静置状态）
            'soc': 96,                   # 真实值：96%
            'bms_temp': 35,              # 真实值：35°C
            'env_temp': 36,              # 真实值：36°C（环境温度）
            'residual_capacity': 51.30,  # 真实值：51.30Ah
            'current_capacity': 53.00,   # 真实值：53.00Ah
            'total_mileage': 18.2,       # 真实值：18.2km
            'vol_differ': 0.862,         # 真实值：0.862V（电压差）
            'average_vol': 0.001,        # 真实值：0.001V（平均电压）
            'latitude': 22.506839,       # 真实GPS纬度
            'longitude': 113.417564,     # 真实GPS经度
            'altitude': 3,               # 真实海拔
            'speed': 0,                  # 真实速度
            'direction': 164,            # 真实方向
            'cell_count': 20,            # 20个电芯
            'temp_count': 2              # 2个温度传感器
        }

        # 真实的单体电压数据（来自XML）
        self.real_cell_voltages = [
            "4.222", "4.223", "4.223", "4.224", "4.223", "4.221", "4.224", "4.225",
            "4.223", "4.225", "4.220", "4.223", "4.224", "4.222", "4.223", "4.224",
            "4.223", "4.223", "4.223", "4.222"
        ]

        # 真实的温度数据
        self.real_temperatures = ["34.00", "33.00"]

        # 当前MOS状态（从控制API同步）
        self.current_mos_status = {
            'chargeMOSStatus': '1',
            'disChargeMOSStatus': '1',
            'DCMOS': '1',
            'chargeMOS': '1'
        }

        # 时间相关
        self.start_time = time.time()

        # 变化范围（更小的变化以保持真实性）
        self.variations = {
            'voltage': 0.003,  # ±3mV
            'current': 2.0,    # ±2A
            'soc': 1,          # ±1%
            'temp': 1.0,       # ±1°C
            'location': 0.0001 # 更小的位置变化
        }
        
    def get_current_time_string(self) -> str:
        """获取当前时间字符串（格式：YYMMDDHHMMSS）"""
        return time.strftime('%y%m%d%H%M%S')

    def generate_cell_voltages(self) -> List[str]:
        """生成真实格式的单体电压数据"""
        voltages = []
        for i, base_voltage in enumerate(self.real_cell_voltages):
            # 在真实电压基础上添加小幅变化
            base_val = float(base_voltage)
            variation = random.uniform(-self.variations['voltage'], self.variations['voltage'])
            new_voltage = base_val + variation
            voltages.append(f"{new_voltage:.3f}")
        return voltages

    def generate_temperatures(self) -> List[str]:
        """生成真实格式的温度数据"""
        temperatures = []
        for base_temp in self.real_temperatures:
            base_val = float(base_temp)
            variation = random.uniform(-self.variations['temp'], self.variations['temp'])
            new_temp = base_val + variation
            temperatures.append(f"{new_temp:.2f}")
        return temperatures

    def generate_battery_status(self) -> Dict[str, str]:
        """生成真实格式的电池状态数据"""
        # 从控制API获取当前MOS状态
        try:
            from api.control import current_mos_status
            charge_mos = str(current_mos_status.get('chargeMOS', 1))
            discharge_mos = str(current_mos_status.get('dischargeMOS', 1))
        except:
            charge_mos = '1'
            discharge_mos = '1'

        # 真实的电池状态结构（基于XML数据）
        return {
            'eDetectOpen': '0',
            'dischargeOverCurrent': '0',
            'eCoreTempHigh': '0',
            'tempDetectOpenCircuit': '0',
            'rent': '0',
            'BMSPowerDown': '0',
            'chargeOverCurrent': '0',
            'DCMOS': charge_mos,
            'validBCode': '0',
            'chargeMOSStatus': charge_mos,
            'eCoreTempUnder': '0',
            'BMSTempOver': '0',
            'BMSFailureStatus': '0',
            'discharge': '0',
            'eCoreTempOver': '0',
            'BMSTempHigh': '0',
            'eDetectOpenCircuit': '0',
            'eCoreOverVol': '0',
            'charge': '0',
            'BMSStandMode': '0',
            'BMSPowerDownMode': '0',
            'cOverCur': '0',
            'chargeMOS': charge_mos,
            'forbidDCDuration': '0',
            'eCoreTempLow': '0',
            'shortCut': '0',
            'forbidDischarge': '0',
            'fillUp': '0',
            'dcOverCur': '0',
            'forbidCharge': '0',
            'eCoreUnderVol': '0',
            'BMSFailure': '0',
            'forbidDC': '0',
            'BMSStand': '0',
            'permitDCDuration': '0',
            'tempDetectOpen': '0',
            'dc': '0',
            'disChargeMOSStatus': discharge_mos
        }
    
    def generate_battery_package_info(self) -> str:
        """生成真实格式的电池包信息JSON字符串"""
        current_time = self.get_current_time_string()

        # 使用真实数据值（微小变化以保持真实性）
        total_voltage = self.base_values['total_voltage']  # 保持84.40V
        total_current = self.base_values['total_current']  # 保持0.00A（静置状态）
        soc = self.base_values['soc']                      # 保持96%
        bms_temp = self.base_values['bms_temp']            # 保持35°C
        residual_capacity = self.base_values['residual_capacity']  # 保持51.30Ah
        current_capacity = self.base_values['current_capacity']    # 保持53.00Ah

        battery_package_info = {
            'infoTime': current_time,
            'batteryId': self.battery_id,
            'batteryId26': self.battery_id,
            'totalVoltage': f"{total_voltage:.2f}",
            'totalCurrent': f"{total_current:.2f}",
            'soc': str(soc),
            'batteryStatus': self.generate_battery_status(),
            'cellQuantity': str(self.base_values['cell_count']),
            'cellVoltageDetail': self.generate_cell_voltages(),
            'tempQuantity': str(self.base_values['temp_count']),
            'tempDetailInfo': self.generate_temperatures(),
            'BMSTemp': str(int(bms_temp)),
            'residualCapacity': f"{residual_capacity:.2f}",
            'currentCapacity': f"{current_capacity:.2f}",
            'loopTimes': '1',
            'sMaxVol': '11.68',
            'sMinVol': '12.35',
            'sMaxSeriesNum': str(random.randint(25, 29)),
            'sMinSeriesNum': str(random.randint(25, 29)),
            'soh': '0',
            'lifeSignal': '78'
        }

        return json.dumps(battery_package_info, ensure_ascii=False)
    
    def generate_base_station_info(self) -> str:
        """生成真实格式的基站信息JSON字符串"""
        current_time = self.get_current_time_string()

        base_station_info = {
            'infoTime': current_time,
            'GSMSignalIntensity': str(random.randint(20, 30)),
            'locationSatNum': str(random.randint(25, 35)),
            'MCC': '460',
            'MNC': '0',
            'LAC': '9715',
            'CELLID': '49865987',
            'DBM': str(random.randint(-80, -60))
        }

        return json.dumps(base_station_info, ensure_ascii=False)

    def generate_bms_status_info(self) -> str:
        """生成真实格式的BMS状态信息JSON字符串"""
        current_time = self.get_current_time_string()

        # 获取当前MOS状态
        try:
            from api.control import current_mos_status
            charge_mos = current_mos_status.get('chargeMOS', 1)
            discharge_mos = current_mos_status.get('dischargeMOS', 1)
        except:
            charge_mos = 1
            discharge_mos = 1

        bms_status_info = {
            'infoTime': current_time,
            'envTemp': str(self.base_values['env_temp']),  # 真实值：36°C
            'alertStatusBit': '0000000000000000',
            'protectStatusBit': '0000000000000000',
            'chargeMosStatus': charge_mos,
            'invalidStatusBit': '00000000',
            'disChargeMosStatus': discharge_mos,
            'bmsStatusBit': '00000000',
            'balanceStatus1Bit': '0000110110000111',
            'balanceStatus2Bit': '0000000010101000',
            'averageVol': str(self.base_values['average_vol']),  # 真实值：0.001
            'volDiffer': str(self.base_values['vol_differ']),    # 真实值：0.862
            'totalMileage': str(self.base_values['total_mileage']),  # 真实值：18.2
            'comStatusBit': '00000011',
            'remoteUpdate': '0',
            'downProgress': '0',
            'updateProgress': '0',
            'codeAsccII': '2,0,,0,111,0',
            'permitDischargeCountDown': str(random.randint(60000, 70000)),
            'forbidDischargeCountDown': str(random.randint(4000, 5000)),
            'totalSumChargeTimes': str(random.randint(4000, 5000)),
            'lastChargeInterval': str(random.randint(4000, 5000)),
            'chargeOrDischargeHighTempTimes': str(random.randint(4000, 5000)),
            'chargeOrDischargeLowTempTimes': str(random.randint(800, 1000)),
            'forceOverDischargeTimes': str(random.randint(800, 1000)),
            'forceOverschargeTimes': str(random.randint(2500, 3500)),
            'forceOverCurrentTimes': str(random.randint(3000, 3500)),
            'forceShortCircleTimes': str(random.randint(2000, 3000)),
            'lowVolPowerOffTimes': str(random.randint(2500, 3000)),
            'exceptionShutDownTimes': str(random.randint(500, 700)),
            'forceResetTimes': '9999',
            'totalSumDischargeTime': str(random.randint(2500, 3000)),
            'totalSumchargeTime': '0',
            'CCID': '89860406192490031343',
            'IEMI': '460042639021943',
            'DTU': 'BMS_F24S3TC_V3_00_17',
            'BMSSV': '3023',
            'BMSHV': '2',
            'protectFlag': '00000000000000000000',
            'alertFlag': '00008001001000000000'
        }

        return json.dumps(bms_status_info, ensure_ascii=False)
    
    def generate_realtime_data(self) -> Dict:
        """生成完整的真实格式实时数据"""
        current_time = self.get_current_time_string()

        # 生成位置变化
        lat_offset = random.uniform(-self.variations['location'], self.variations['location'])
        lon_offset = random.uniform(-self.variations['location'], self.variations['location'])

        latitude = self.base_values['latitude'] + lat_offset
        longitude = self.base_values['longitude'] + lon_offset
        altitude = self.base_values['altitude'] + random.randint(-2, 5)
        speed = random.randint(0, 10)
        direction = random.randint(0, 360)

        # 构建真实格式的数据结构
        realtime_data = {
            'mobile': self.client_id,
            'heartBeatTime': current_time,
            'latitude': f"{latitude:.6f}",
            'longitude': f"{longitude:.6f}",
            'altitude': str(altitude),
            'speed': str(speed),
            'direction': str(direction),
            'time': current_time,
            'accStatus': '0',
            'isLocation': '1',
            'multiLink': '0',
            'operateS': '1',
            'defendS': '0',
            'assistLoc': '0',
            'shakeS': '0',
            'addFlag': '1',
            'baseStationInfo': self.generate_base_station_info(),
            'batteryId': self.battery_id,
            'batteryId26': self.battery_id,
            'bmsVersion': '22',
            'batteryPackageInfo': self.generate_battery_package_info(),
            'bmsStatusInfo': self.generate_bms_status_info(),
            'bmsStatusBit': '00000000',
            'otaInfo': json.dumps({
                'type': '00',
                'state': '00',
                'event': '01',
                'current': 0,
                'total': 0,
                'percent': 0,
                'errorCode': '00',
                'dtuVersion': 'BMS_F24S3TC_V3_00_17',
                'mcuVersion': '',
                'Slave1Num': 0,
                'Slave1Version': [],
                'Slave2Num': 0,
                'Slave2Version': []
            }, ensure_ascii=False)
        }

        return realtime_data
    
    def generate_device_parameters(self) -> Dict:
        """生成真实格式的设备参数"""
        return {
            'chargeCurrentCalibrationKValue': str(random.randint(995, 1005)),
            'chargingOverCurrentProtectionValue': str(random.randint(40, 50)),
            'dischargeOverCurrent1ProtectionDelay': str(random.randint(5, 10)),
            'overCurrentProtectionRecoveryTimeDelay': str(random.randint(250, 350)),
            'rechargeOverCurrentAlarmRecoveryValue': str(random.randint(15, 25)),
            'dischargeOverCurrent2ProtectionValue': str(random.randint(70, 80)),
            'dischargeOverCurrent2ProtectionDelay': str(random.randint(1200, 1400)),
            'shortCircuitProtectionDelay': str(random.randint(400, 450)),
            'zeroCurrentCalibrationBValue': f"{random.uniform(2.5, 3.5):.1f}",
            'dischargeCurrentCalibrationKValue': str(random.randint(995, 1005)),
            'chargingOverCurrentAlarmValue': str(random.randint(35, 45)),
            'chargeOverCurrentProtectionDelay': str(random.randint(5, 10)),
            'dischargeOverCurrentAlarmValue': str(random.randint(50, 60)),
            'dischargeOverCurrentAlarmRecoveryValue': str(random.randint(35, 45)),
            'dischargeOverCurrentRecoveryDelay': str(random.randint(50, 70)),
            'shortCircuitProtectionCurrent': str(random.randint(450, 550)),
            'dischargeOverCurrent1ProtectionValue': str(random.randint(60, 70))
        }

    def generate_user_battery_list(self, user_id: str) -> List[Dict]:
        """生成用户电池列表"""
        return [{
            'userBattery': {
                'id': '1934498155206926338',
                'userId': user_id,
                'clientId': self.client_id,
                'batteryCode': self.battery_id,
                'batteryType': '72V55Ah',
                'name': '请修改您的电池备注名！',
                'owner': user_id,
                'createBy': 'wanyang_ZZxXlsqAFg',
                'createTime': '2025-06-18 14:27:56',
                'updateBy': None,
                'updateTime': None
            },
            'batteryImg': 'temp/72V55Ah_1745995372500.png'
        }]

    def generate_battery_models(self) -> List[Dict]:
        """生成电池型号列表"""
        models = [
            {
                'id': '1909181926745362434',
                'model': '60V55Ah',
                'sign': '捕捉电一刻，续航无忧',
                'image': 'temp/60V55Ah_1744596934747.png',
                'more': '60V55Ah是我公司推出的高性能锂离子电池产品，采用先进的三元锂离子技术...',
                'type': '锂离子动力电池',
                'size': '220*160*320mm',
                'chargeCurrent': '20A',
                'dischargeCurrent': '60A',
                'voltage': '71.4V',
                'weight': '20±1KG',
                'waterproofRating': 'IP65',
                'createBy': 'admin',
                'createTime': '2025-04-07 17:50:17',
                'updateBy': 'admin',
                'updateTime': '2025-05-06 16:55:58'
            },
            {
                'id': '1909182323329388545',
                'model': '60V50Ah',
                'sign': '性价比捕捉电，持久动力',
                'image': 'temp/60V50Ah_1744596926802.png',
                'more': '60V50Ah电芯采用高品质三元锂，具有优异的安全性能和稳定性...',
                'type': '锂离子动力电池',
                'size': '175*165*323mm',
                'chargeCurrent': '20A',
                'dischargeCurrent': '60A',
                'voltage': '71.4V',
                'weight': '15±1KG',
                'waterproofRating': 'IP65',
                'createBy': 'admin',
                'createTime': '2025-04-07 17:51:51',
                'updateBy': 'admin',
                'updateTime': '2025-05-06 16:55:55'
            }
        ]
        return models

    def update_mos_status(self, charge_mos: int, discharge_mos: int):
        """更新MOS状态"""
        self.current_mos_status = {
            'chargeMOSStatus': str(charge_mos),
            'disChargeMOSStatus': str(discharge_mos),
            'DCMOS': str(charge_mos),
            'chargeMOS': str(charge_mos)
        }

class RealAlertSimulator:
    """基于真实数据的警报模拟器"""

    def __init__(self):
        # 基于真实BMS状态位的警报类型
        self.alert_types = {
            'voltage': ['单体过压', '单体欠压', '总压过高', '总压过低'],
            'current': ['充电过流', '放电过流', '短路保护'],
            'temperature': ['充电高温', '充电低温', '放电高温', '放电低温', 'BMS高温'],
            'system': ['通信故障', 'BMS故障', '平衡异常', '传感器故障']
        }

    def parse_protection_and_warning(self, bms_status_info: str) -> Dict:
        """解析BMS状态信息中的保护和警告"""
        try:
            bms_data = json.loads(bms_status_info)

            alerts = []
            protections = []

            # 解析警告状态位
            alert_status = bms_data.get('alertStatusBit', '0000000000000000')
            if alert_status != '0000000000000000':
                alerts.extend(self.decode_status_bits(alert_status, 'alert'))

            # 解析保护状态位
            protect_status = bms_data.get('protectStatusBit', '0000000000000000')
            if protect_status != '0000000000000000':
                protections.extend(self.decode_status_bits(protect_status, 'protect'))

            # 检查MOS状态
            charge_mos = bms_data.get('chargeMosStatus', 1)
            discharge_mos = bms_data.get('disChargeMosStatus', 1)

            if charge_mos == 1:
                alerts.append('充电MOS正常闭合')
            else:
                alerts.append('充电MOS异常断开')

            if discharge_mos == 1:
                alerts.append('放电MOS正常闭合')
            else:
                alerts.append('放电MOS异常断开')

            return {
                'alerts': alerts,
                'protections': protections
            }

        except Exception as e:
            return {
                'alerts': ['数据解析异常'],
                'protections': []
            }

    def decode_status_bits(self, status_bits: str, status_type: str) -> List[str]:
        """解码状态位"""
        decoded = []

        # 简化的状态位解码（实际应根据BMS协议文档）
        if status_type == 'alert':
            if status_bits[-1] == '1':
                decoded.append('电压警告')
            if status_bits[-2] == '1':
                decoded.append('电流警告')
            if status_bits[-3] == '1':
                decoded.append('温度警告')
        elif status_type == 'protect':
            if status_bits[-1] == '1':
                decoded.append('过压保护')
            if status_bits[-2] == '1':
                decoded.append('过流保护')
            if status_bits[-3] == '1':
                decoded.append('过温保护')

        return decoded

# 全局真实数据模拟器实例
real_battery_simulator = RealBatteryDataSimulator()
real_alert_simulator = RealAlertSimulator()

# 保持向后兼容性的别名
battery_simulator = real_battery_simulator
alert_simulator = real_alert_simulator

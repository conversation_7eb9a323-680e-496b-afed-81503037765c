#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from dataclasses import dataclass, field
from typing import Dict, Optional, List
import time

@dataclass
class DeviceParameters:
    """设备参数模型"""
    client_id: str
    
    # 电流校准参数
    charge_current_calibration_k_value: str = "1000"
    discharge_current_calibration_k_value: str = "1001"
    zero_current_calibration_b_value: str = "3.0"
    
    # 过流保护参数
    charging_over_current_protection_value: str = "45"
    charging_over_current_alarm_value: str = "40"
    charge_over_current_protection_delay: str = "8"
    
    discharge_over_current_alarm_value: str = "55"
    discharge_over_current_alarm_recovery_value: str = "40"
    discharge_over_current_1_protection_value: str = "66"
    discharge_over_current_1_protection_delay: str = "8"
    discharge_over_current_2_protection_value: str = "77"
    discharge_over_current_2_protection_delay: str = "1280"
    discharge_over_current_recovery_delay: str = "60"
    
    # 短路保护参数
    short_circuit_protection_current: str = "500"
    short_circuit_protection_delay: str = "406"
    
    # 其他保护参数
    over_current_protection_recovery_time_delay: str = "300"
    recharge_over_current_alarm_recovery_value: str = "20"
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'chargeCurrentCalibrationKValue': self.charge_current_calibration_k_value,
            'chargingOverCurrentProtectionValue': self.charging_over_current_protection_value,
            'dischargeOverCurrent1ProtectionDelay': self.discharge_over_current_1_protection_delay,
            'overCurrentProtectionRecoveryTimeDelay': self.over_current_protection_recovery_time_delay,
            'rechargeOverCurrentAlarmRecoveryValue': self.recharge_over_current_alarm_recovery_value,
            'dischargeOverCurrent2ProtectionValue': self.discharge_over_current_2_protection_value,
            'dischargeOverCurrent2ProtectionDelay': self.discharge_over_current_2_protection_delay,
            'shortCircuitProtectionDelay': self.short_circuit_protection_delay,
            'zeroCurrentCalibrationBValue': self.zero_current_calibration_b_value,
            'dischargeCurrentCalibrationKValue': self.discharge_current_calibration_k_value,
            'chargingOverCurrentAlarmValue': self.charging_over_current_alarm_value,
            'chargeOverCurrentProtectionDelay': self.charge_over_current_protection_delay,
            'dischargeOverCurrentAlarmValue': self.discharge_over_current_alarm_value,
            'dischargeOverCurrentAlarmRecoveryValue': self.discharge_over_current_alarm_recovery_value,
            'dischargeOverCurrentRecoveryDelay': self.discharge_over_current_recovery_delay,
            'shortCircuitProtectionCurrent': self.short_circuit_protection_current,
            'dischargeOverCurrent1ProtectionValue': self.discharge_over_current_1_protection_value
        }

@dataclass
class BaseStationInfo:
    """基站信息模型"""
    info_time: str
    gsm_signal_intensity: str = "25"
    location_sat_num: str = "30"
    mcc: str = "460"
    mnc: str = "0"
    lac: str = "9715"
    cell_id: str = "49865987"
    dbm: str = "-70"
    
    def to_json_string(self) -> str:
        """转换为JSON字符串"""
        import json
        data = {
            'infoTime': self.info_time,
            'GSMSignalIntensity': self.gsm_signal_intensity,
            'locationSatNum': self.location_sat_num,
            'MCC': self.mcc,
            'MNC': self.mnc,
            'LAC': self.lac,
            'CELLID': self.cell_id,
            'DBM': self.dbm
        }
        return json.dumps(data, ensure_ascii=False)

@dataclass
class BMSStatusInfo:
    """BMS状态信息模型"""
    info_time: str
    env_temp: str = "36"
    alert_status_bit: str = "0000000000000000"
    protect_status_bit: str = "0000000000000000"
    charge_mos_status: int = 1
    invalid_status_bit: str = "00000000"
    dis_charge_mos_status: int = 1
    bms_status_bit: str = "00000000"
    balance_status_1_bit: str = "0000110110000111"
    balance_status_2_bit: str = "0000000010101000"
    average_vol: str = "0.001"
    vol_differ: str = "0.862"
    total_mileage: str = "18.2"
    com_status_bit: str = "00000011"
    remote_update: str = "0"
    down_progress: str = "0"
    update_progress: str = "0"
    code_ascci_ii: str = "2,0,,0,111,0"
    
    # 各种计数器
    permit_discharge_duration_count_down: str = "64241"
    forbid_discharge_duration_count_down: str = "4300"
    total_sum_charge_times: str = "4250"
    last_charge_interval: str = "4350"
    charge_or_discharge_high_temp_times: str = "4250"
    charge_or_discharge_low_temp_times: str = "860"
    force_over_discharge_times: str = "840"
    force_overscharge_times: str = "3000"
    force_over_current_times: str = "3150"
    force_short_circle_times: str = "2500"
    low_vol_power_off_times: str = "2800"
    exception_shut_down_times: str = "600"
    force_reset_times: str = "9999"
    total_sum_discharge_time: str = "2700"
    total_sum_charge_time: str = "0"
    
    # 设备标识
    ccid: str = "89860406192490031343"
    iemi: str = "460042639021943"
    dtu: str = "BMS_F24S3TC_V3_00_17"
    bms_sv: str = "3023"
    bms_hv: str = "2"
    protect_flag: str = "00000000000000000000"
    alert_flag: str = "00008001001000000000"
    
    def to_json_string(self) -> str:
        """转换为JSON字符串"""
        import json
        data = {
            'infoTime': self.info_time,
            'envTemp': self.env_temp,
            'alertStatusBit': self.alert_status_bit,
            'protectStatusBit': self.protect_status_bit,
            'chargeMosStatus': self.charge_mos_status,
            'invalidStatusBit': self.invalid_status_bit,
            'disChargeMosStatus': self.dis_charge_mos_status,
            'bmsStatusBit': self.bms_status_bit,
            'balanceStatus1Bit': self.balance_status_1_bit,
            'balanceStatus2Bit': self.balance_status_2_bit,
            'averageVol': self.average_vol,
            'volDiffer': self.vol_differ,
            'totalMileage': self.total_mileage,
            'comStatusBit': self.com_status_bit,
            'remoteUpdate': self.remote_update,
            'downProgress': self.down_progress,
            'updateProgress': self.update_progress,
            'codeAsccII': self.code_ascci_ii,
            'permitDischargeCountDown': self.permit_discharge_duration_count_down,
            'forbidDischargeCountDown': self.forbid_discharge_duration_count_down,
            'totalSumChargeTimes': self.total_sum_charge_times,
            'lastChargeInterval': self.last_charge_interval,
            'chargeOrDischargeHighTempTimes': self.charge_or_discharge_high_temp_times,
            'chargeOrDischargeLowTempTimes': self.charge_or_discharge_low_temp_times,
            'forceOverDischargeTimes': self.force_over_discharge_times,
            'forceOverschargeTimes': self.force_overscharge_times,
            'forceOverCurrentTimes': self.force_over_current_times,
            'forceShortCircleTimes': self.force_short_circle_times,
            'lowVolPowerOffTimes': self.low_vol_power_off_times,
            'exceptionShutDownTimes': self.exception_shut_down_times,
            'forceResetTimes': self.force_reset_times,
            'totalSumDischargeTime': self.total_sum_discharge_time,
            'totalSumchargeTime': self.total_sum_charge_time,
            'CCID': self.ccid,
            'IEMI': self.iemi,
            'DTU': self.dtu,
            'BMSSV': self.bms_sv,
            'BMSHV': self.bms_hv,
            'protectFlag': self.protect_flag,
            'alertFlag': self.alert_flag
        }
        return json.dumps(data, ensure_ascii=False)

@dataclass
class OTAInfo:
    """OTA升级信息模型"""
    type: str = "00"
    state: str = "00"
    event: str = "01"
    current: int = 0
    total: int = 0
    percent: int = 0
    error_code: str = "00"
    dtu_version: str = "BMS_F24S3TC_V3_00_17"
    mcu_version: str = ""
    slave1_num: int = 0
    slave1_version: List[str] = field(default_factory=list)
    slave2_num: int = 0
    slave2_version: List[str] = field(default_factory=list)
    
    def to_json_string(self) -> str:
        """转换为JSON字符串"""
        import json
        data = {
            'type': self.type,
            'state': self.state,
            'event': self.event,
            'current': self.current,
            'total': self.total,
            'percent': self.percent,
            'errorCode': self.error_code,
            'dtuVersion': self.dtu_version,
            'mcuVersion': self.mcu_version,
            'Slave1Num': self.slave1_num,
            'Slave1Version': self.slave1_version,
            'Slave2Num': self.slave2_num,
            'Slave2Version': self.slave2_version
        }
        return json.dumps(data, ensure_ascii=False)

@dataclass
class Device:
    """设备模型"""
    client_id: str
    mobile: str
    battery_id: str
    user_id: str
    device_key: str
    
    # 设备状态
    online: bool = True
    last_heartbeat: float = field(default_factory=time.time)
    
    # 关联的组件
    parameters: Optional[DeviceParameters] = None
    base_station_info: Optional[BaseStationInfo] = None
    bms_status_info: Optional[BMSStatusInfo] = None
    ota_info: Optional[OTAInfo] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.parameters is None:
            self.parameters = DeviceParameters(client_id=self.client_id)
        
        current_time = str(int(time.time()))
        if self.base_station_info is None:
            self.base_station_info = BaseStationInfo(info_time=current_time)
        
        if self.bms_status_info is None:
            self.bms_status_info = BMSStatusInfo(info_time=current_time)
        
        if self.ota_info is None:
            self.ota_info = OTAInfo()
    
    def update_heartbeat(self):
        """更新心跳时间"""
        self.last_heartbeat = time.time()
        self.online = True
    
    def is_online(self, timeout: int = 300) -> bool:
        """检查设备是否在线（默认5分钟超时）"""
        return time.time() - self.last_heartbeat < timeout

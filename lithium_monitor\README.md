# 锂电监控系统

基于Flask的锂电池监控和控制系统，支持实时数据监控、MOS开关控制、历史数据分析等功能。

## 🚀 功能特性

### 📊 数据获取层
- **电池型号管理** - 支持多种电池型号的规格查询
- **用户设备管理** - 管理用户绑定的电池设备
- **实时数据监控** - 获取电池电压、电流、温度、SOC等实时数据
- **GPS位置追踪** - 实时获取设备位置信息
- **设备参数查询** - 获取BMS设备的配置参数

### 🎛️ 控制请求层
- **MOS开关控制** - 支持充电/放电MOS的独立或同时控制
  - `cmdId=40`: 关闭放电MOS
  - `cmdId=41`: 打开放电MOS
  - `cmdId=08`: 打开充/放电MOS（同时）
  - `cmdId=09`: 关闭充/放电MOS（同时）
- **GPS手动定位** - 支持手动GPS定位功能
- **控制历史记录** - 记录所有控制操作的历史

### 📈 数据展示层
- **实时监控仪表板** - 直观显示关键电池指标
- **数据趋势图表** - 实时数据变化趋势可视化
- **单体电压分布** - 显示所有电芯的电压分布情况
- **状态警告系统** - 实时显示设备状态和警告信息
- **设备控制面板** - 友好的MOS控制操作界面

## 🏗️ 项目结构

```
lithium_monitor/
├── app.py                 # Flask主应用
├── config.py              # 配置文件
├── requirements.txt       # 依赖包
├── .env                   # 环境变量
├── README.md              # 项目说明
├── static/               # 静态文件
│   ├── css/              # CSS样式文件
│   ├── js/               # JavaScript文件
│   │   └── common.js     # 通用JS函数
│   └── images/           # 图片资源
├── templates/            # HTML模板
│   ├── base.html         # 基础模板
│   ├── dashboard.html    # 监控仪表板
│   └── control.html      # 控制面板
├── api/                  # API模块
│   ├── __init__.py
│   ├── battery_data.py   # 电池数据API
│   ├── control.py        # 控制API
│   └── auth.py           # 认证模块
├── models/               # 数据模型
│   ├── __init__.py
│   ├── battery.py        # 电池相关模型
│   └── device.py         # 设备相关模型
└── utils/                # 工具函数
    ├── __init__.py
    ├── data_simulator.py # 数据模拟器
    └── helpers.py        # 辅助函数
```

## 🔧 安装和运行

### 1. 环境要求
- Python 3.8+
- pip

### 2. 安装依赖
```bash
cd lithium_monitor
pip install -r requirements.txt
```

### 3. 配置环境变量
复制 `.env` 文件并根据需要修改配置：
```bash
# Flask环境配置
FLASK_ENV=development
FLASK_DEBUG=True

# 安全密钥
SECRET_KEY=your-secret-key
JWT_SECRET_KEY=your-jwt-secret-key

# 锂电监控系统配置
BATTERY_CLIENT_ID=380074209785
BATTERY_KEY=79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c
```

### 4. 运行应用
```bash
python app.py
```

应用将在 `http://127.0.0.1:5000` 启动

## 📱 使用说明

### 访问地址
- **监控仪表板**: http://127.0.0.1:5000/dashboard
- **设备控制**: http://127.0.0.1:5000/control
- **健康检查**: http://127.0.0.1:5000/health

### API接口

#### 数据获取接口
- `GET /api/battery/models` - 获取电池型号列表
- `GET /api/battery/user-batteries` - 获取用户电池列表
- `GET /api/battery/realtime` - 获取实时电池数据
- `GET /api/battery/device-parameters` - 获取设备参数

#### 控制接口
- `GET /api/control/terminal` - 终端控制接口
- `GET /api/control/mos-status` - 获取MOS状态
- `GET /api/control/commands` - 获取控制命令列表
- `GET /api/control/history` - 获取控制历史

#### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/verify` - 验证Token
- `POST /api/auth/refresh` - 刷新Token

## 🔐 认证机制

系统使用JWT Token进行身份验证：
- Header: `X-Access-Token`
- 控制接口需要额外的key和clientId参数
- 默认用户名/密码: `admin/admin123`

## 📊 数据模拟

系统内置数据模拟器，可以生成：
- 实时电池数据（电压、电流、温度、SOC）
- 单体电压数据（20个电芯）
- GPS位置信息
- 设备状态信息
- 历史数据记录

## 🛡️ 安全特性

- JWT Token认证
- 控制操作确认机制
- 参数验证和错误处理
- 操作日志记录
- 连接状态监控

## 🔧 开发说明

### 添加新的控制命令
1. 在 `config.py` 中的 `CONTROL_COMMANDS` 添加新命令
2. 在 `api/control.py` 中的 `execute_control_command` 函数添加处理逻辑
3. 在前端控制面板添加对应的按钮

### 添加新的数据类型
1. 在 `models/battery.py` 中定义数据模型
2. 在 `utils/data_simulator.py` 中添加模拟数据生成
3. 在 `api/battery_data.py` 中添加API接口
4. 在前端页面添加显示组件

## 📝 更新日志

### v1.0.0 (2025-06-18)
- ✅ 完成基础项目结构
- ✅ 实现数据获取API
- ✅ 实现MOS控制功能
- ✅ 完成监控仪表板
- ✅ 完成设备控制面板
- ✅ 集成数据模拟器
- ✅ 实现JWT认证

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 技术支持

如有问题或建议，请联系技术支持团队。

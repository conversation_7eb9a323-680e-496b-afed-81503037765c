#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import json
from flask import Blueprint, request, jsonify, current_app
from api.auth import require_auth

# 创建控制蓝图
control_bp = Blueprint('control', __name__)

# 全局变量存储当前MOS状态
current_mos_status = {
    'chargeMOS': 1,  # 1=开启, 0=关闭
    'dischargeMOS': 1,
    'last_update': time.time()
}

@control_bp.route('/terminal', methods=['GET'])
def terminal_control():
    """终端控制接口 - 对应 /jeecg-boot/fnjbattery/terminalControl"""
    
    key = request.args.get('key')
    client_id = request.args.get('clientId')
    cmd_id = request.args.get('cmdId')
    
    # 验证必要参数
    if not all([key, client_id, cmd_id]):
        return jsonify({
            'success': False,
            'message': '缺少必要参数: key, clientId, cmdId',
            'code': 400
        }), 400
    
    # 验证key和clientId
    expected_key = current_app.config['BATTERY_CONFIG']['DEFAULT_KEY']
    expected_client_id = current_app.config['BATTERY_CONFIG']['DEFAULT_CLIENT_ID']
    
    if key != expected_key or client_id != expected_client_id:
        return jsonify({
            'success': False,
            'message': '无效的key或clientId',
            'code': 403
        }), 403
    
    # 验证控制命令
    if cmd_id not in current_app.config['CONTROL_COMMANDS']:
        return jsonify({
            'success': False,
            'message': f'无效的控制命令: {cmd_id}',
            'code': 400
        }), 400
    
    print(f"🎛️ 发送真实控制命令: cmdId={cmd_id}, clientId={client_id}")

    # 直接发送控制命令到万洋锂电服务器
    import requests
    try:
        wanyang_url = "https://sys.wyzxcn.com/jeecg-boot/fnjbattery/terminalControl"
        params = {'key': key, 'clientId': client_id, 'cmdId': cmd_id}

        print(f"📤 发送控制命令到万洋锂电服务器: {wanyang_url}")
        print(f"📋 控制参数: {params}")

        response = requests.get(wanyang_url, params=params, timeout=10)

        if response.status_code == 200:
            print("✅ 控制命令发送成功")

            # 更新本地MOS状态记录
            update_local_mos_status(cmd_id)

            try:
                # 尝试解析万洋锂电服务器的响应
                server_response = response.json()
                return jsonify(server_response)
            except:
                # 如果不是JSON格式，返回成功响应
                return jsonify({
                    'success': True,
                    'message': f'控制命令执行成功: {current_app.config["CONTROL_COMMANDS"][cmd_id]}',
                    'code': 200,
                    'result': {
                        'command_id': cmd_id,
                        'command_desc': current_app.config['CONTROL_COMMANDS'][cmd_id],
                        'server_response': response.text,
                        'execution_time': int(time.time() * 1000)
                    },
                    'timestamp': int(time.time() * 1000)
                })
        else:
            print(f"❌ 万洋锂电服务器控制命令失败: {response.status_code}")
            return jsonify({
                'success': False,
                'message': f'万洋锂电服务器控制命令失败: {response.status_code}',
                'code': response.status_code
            }), response.status_code

    except requests.exceptions.Timeout:
        print("⏰ 控制命令发送超时")
        return jsonify({
            'success': False,
            'message': '控制命令发送超时',
            'code': 408
        }), 408

    except Exception as e:
        print(f"❌ 控制命令发送异常: {e}")
        return jsonify({
            'success': False,
            'message': f'控制命令发送失败: {str(e)}',
            'code': 500
        }), 500

def update_local_mos_status(cmd_id):
    """更新本地MOS状态记录"""
    global current_mos_status

    # 更新MOS状态
    if cmd_id == '40':  # 关闭放电MOS
        current_mos_status['dischargeMOS'] = 0
    elif cmd_id == '41':  # 打开放电MOS
        current_mos_status['dischargeMOS'] = 1
    elif cmd_id == '08':  # 打开充/放电MOS（同时）
        current_mos_status['chargeMOS'] = 1
        current_mos_status['dischargeMOS'] = 1
    elif cmd_id == '09':  # 关闭充/放电MOS（同时）
        current_mos_status['chargeMOS'] = 0
        current_mos_status['dischargeMOS'] = 0

    current_mos_status['last_update'] = time.time()

def execute_control_command(cmd_id):
    """执行控制命令"""
    global current_mos_status

    command_desc = current_app.config['CONTROL_COMMANDS'][cmd_id]

    # 更新MOS状态
    if cmd_id == '40':  # 关闭放电MOS
        current_mos_status['dischargeMOS'] = 0
        alerts = ['放电MOS已关闭']

    elif cmd_id == '41':  # 打开放电MOS
        current_mos_status['dischargeMOS'] = 1
        alerts = ['放电MOS已打开']

    elif cmd_id == '08':  # 打开充/放电MOS（同时）
        current_mos_status['chargeMOS'] = 1
        current_mos_status['dischargeMOS'] = 1
        alerts = ['充电MOS已打开', '放电MOS已打开']

    elif cmd_id == '09':  # 关闭充/放电MOS（同时）
        current_mos_status['chargeMOS'] = 0
        current_mos_status['dischargeMOS'] = 0
        alerts = ['充电MOS已关闭', '放电MOS已关闭']

    else:
        alerts = [f'未知命令: {cmd_id}']

    current_mos_status['last_update'] = time.time()

    # 同步更新数据模拟器的MOS状态
    try:
        from utils.data_simulator import real_battery_simulator
        real_battery_simulator.update_mos_status(
            current_mos_status['chargeMOS'],
            current_mos_status['dischargeMOS']
        )
    except ImportError:
        pass

    # 返回真实格式的响应
    return {
        'command_id': cmd_id,
        'command_desc': command_desc,
        'alerts': alerts,
        'protections': [],
        'mos_status': {
            'chargeMOS': current_mos_status['chargeMOS'],
            'dischargeMOS': current_mos_status['dischargeMOS'],
            'chargeMOSStatus': str(current_mos_status['chargeMOS']),
            'disChargeMOSStatus': str(current_mos_status['dischargeMOS'])
        },
        'execution_time': int(time.time() * 1000),
        'device_response': True
    }

@control_bp.route('/mos-status', methods=['GET'])
def get_mos_status():
    """获取当前MOS状态"""
    global current_mos_status
    
    return jsonify({
        'success': True,
        'message': 'MOS状态获取成功',
        'code': 200,
        'result': {
            'chargeMOS': current_mos_status['chargeMOS'],
            'dischargeMOS': current_mos_status['dischargeMOS'],
            'last_update': current_mos_status['last_update'],
            'status_text': {
                'chargeMOS': '开启' if current_mos_status['chargeMOS'] else '关闭',
                'dischargeMOS': '开启' if current_mos_status['dischargeMOS'] else '关闭'
            }
        },
        'timestamp': int(time.time() * 1000)
    })

@control_bp.route('/gps', methods=['GET'])
def gps_manual():
    """GPS手动定位 - 对应 /jeecg-boot/gps/manual"""

    lat = request.args.get('lat')
    lon = request.args.get('lon')

    if not lat or not lon:
        return jsonify({
            'success': False,
            'message': '缺少纬度或经度参数',
            'code': 400
        }), 400

    try:
        latitude = float(lat)
        longitude = float(lon)

        # 验证GPS坐标范围
        if not (-90 <= latitude <= 90) or not (-180 <= longitude <= 180):
            raise ValueError("坐标超出有效范围")

    except ValueError as e:
        return jsonify({
            'success': False,
            'message': f'坐标格式错误: {str(e)}',
            'code': 400
        }), 400

    # 更新数据模拟器中的GPS位置
    try:
        from utils.data_simulator import real_battery_simulator
        real_battery_simulator.base_values['latitude'] = latitude
        real_battery_simulator.base_values['longitude'] = longitude
    except ImportError:
        pass

    # 真实格式的GPS定位响应
    return jsonify({
        'success': True,
        'message': 'GPS定位成功',
        'code': 200,
        'result': {
            'latitude': latitude,
            'longitude': longitude,
            'precision': '高精度',
            'location_type': '手动定位',
            'update_time': int(time.time() * 1000),
            'address': f'经度: {longitude:.6f}, 纬度: {latitude:.6f}',
            'coordinate_system': 'WGS84'
        },
        'timestamp': int(time.time() * 1000)
    })

@control_bp.route('/commands', methods=['GET'])
def get_control_commands():
    """获取所有可用的控制命令"""
    
    commands = []
    for cmd_id, desc in current_app.config['CONTROL_COMMANDS'].items():
        commands.append({
            'cmd_id': cmd_id,
            'description': desc,
            'category': get_command_category(cmd_id)
        })
    
    return jsonify({
        'success': True,
        'message': '控制命令列表获取成功',
        'code': 200,
        'result': commands,
        'timestamp': int(time.time() * 1000)
    })

def get_command_category(cmd_id):
    """获取命令分类"""
    if cmd_id in ['40', '41']:
        return '放电控制'
    elif cmd_id in ['08', '09']:
        return '充放电控制'
    else:
        return '其他控制'

@control_bp.route('/history', methods=['GET'])
def get_control_history():
    """获取控制历史记录（模拟数据）"""
    
    # 模拟控制历史记录
    history = [
        {
            'id': 1,
            'cmd_id': '08',
            'command': '打开充/放电MOS（同时）',
            'status': 'success',
            'timestamp': int(time.time() * 1000) - 300000,
            'operator': 'admin'
        },
        {
            'id': 2,
            'cmd_id': '40',
            'command': '关闭放电MOS',
            'status': 'success',
            'timestamp': int(time.time() * 1000) - 180000,
            'operator': 'admin'
        },
        {
            'id': 3,
            'cmd_id': '41',
            'command': '打开放电MOS',
            'status': 'success',
            'timestamp': int(time.time() * 1000) - 60000,
            'operator': 'admin'
        }
    ]
    
    return jsonify({
        'success': True,
        'message': '控制历史获取成功',
        'code': 200,
        'result': {
            'records': history,
            'total': len(history)
        },
        'timestamp': int(time.time() * 1000)
    })

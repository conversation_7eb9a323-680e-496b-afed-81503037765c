#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import base64
import json
import urllib.parse
from collections import defaultdict

def decode_base64_response(base64_data):
    """解码base64响应数据"""
    try:
        decoded = base64.b64decode(base64_data).decode('utf-8')
        # 分离HTTP头和JSON数据
        parts = decoded.split('\r\n\r\n', 1)
        if len(parts) == 1:
            parts = decoded.split('\n\n', 1)
        if len(parts) > 1:
            return parts[1]  # 返回JSON部分
        return decoded
    except Exception as e:
        print(f"解码错误: {e}")
        return None

def analyze_api_endpoints():
    """分析所有API端点"""
    
    # 从XML中提取的所有API路径
    api_paths = [
        "/jeecg-boot/posters/posters/getNewest",
        "/jeecg-boot/wybattery/wyBatteryInfor/list",
        "/jeecg-boot/battery/userBattery/api/list?userId=1933343591078334465",
        "/jeecg-boot/fnjbattery/realTime?key=79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c&clientId=380074209785",
        "/jeecg-boot/fnjbattery/parseProtectionAndWarning",
        "/jeecg-boot/fnjbattery/deviceParameters?clientId=380074209785&key=79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c",
        "/jeecg-boot/gps/manual?lat=22.506839&lon=113.417564",
        "/jeecg-boot/fnjbattery/parseAlarmAndBattery",
        "/jeecg-boot/agreement/privacyAgreement/queryByName?name=控制放电电流免责说明",
        "/jeecg-boot/fnjbattery/terminalControl?key=79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c&clientId=380074209785&cmdId=40",
        "/jeecg-boot/fnjbattery/terminalControl?key=79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c&clientId=380074209785&cmdId=41",
        "/jeecg-boot/fnjbattery/terminalControl?key=79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c&clientId=380074209785&cmdId=08",
        "/jeecg-boot/fnjbattery/terminalControl?key=79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c&clientId=380074209785&cmdId=09"
    ]
    
    # 分类API接口
    data_apis = []
    control_apis = []
    config_apis = []
    other_apis = []
    
    for path in api_paths:
        if "terminalControl" in path:
            control_apis.append(path)
        elif any(keyword in path for keyword in ["realTime", "parseProtection", "parseAlarm", "list", "deviceParameters"]):
            data_apis.append(path)
        elif any(keyword in path for keyword in ["agreement", "gps", "posters"]):
            config_apis.append(path)
        else:
            other_apis.append(path)
    
    print("=" * 80)
    print("锂电监控系统 - 完整API接口分析报告")
    print("=" * 80)
    print()
    
    print("📊 **数据获取接口** (Data Acquisition APIs)")
    print("-" * 50)
    for i, api in enumerate(data_apis, 1):
        base_path = api.split('?')[0]
        print(f"{i}. {base_path}")
        if "?" in api:
            params = api.split('?')[1]
            print(f"   参数: {params}")
    print()
    
    print("🎛️ **控制接口** (Control APIs)")
    print("-" * 50)
    control_commands = {}
    for api in control_apis:
        if "cmdId=" in api:
            cmd_id = api.split("cmdId=")[1].split("&")[0]
            control_commands[cmd_id] = api
    
    print("核心控制接口: /jeecg-boot/fnjbattery/terminalControl")
    print("支持的控制命令 (cmdId):")
    
    cmd_descriptions = {
        "40": "关闭放电MOS",
        "41": "打开放电MOS", 
        "08": "打开充/放电MOS（同时）",
        "09": "关闭充/放电MOS（同时）"
    }
    
    for cmd_id in sorted(control_commands.keys()):
        desc = cmd_descriptions.get(cmd_id, "未知命令")
        print(f"   - cmdId={cmd_id}: {desc}")
    print()
    
    print("⚙️ **配置和其他接口** (Configuration APIs)")
    print("-" * 50)
    for i, api in enumerate(config_apis, 1):
        base_path = api.split('?')[0]
        print(f"{i}. {base_path}")
    print()

def analyze_control_sequence():
    """分析控制序列"""
    print("🔄 **控制操作序列分析**")
    print("-" * 50)
    print("基于抓包时间分析，发现以下控制操作序列：")
    print()
    print("时间: 14:38-14:40")
    print("操作序列:")
    print("1. cmdId=40 → 关闭放电MOS")
    print("2. cmdId=41 → 打开放电MOS")
    print("3. cmdId=08 → 打开充/放电MOS（同时）")
    print("4. cmdId=09 → 关闭充/放电MOS（同时）")
    print("5. cmdId=08 → 再次打开充/放电MOS")
    print()
    print("💡 **控制逻辑推断:**")
    print("- 系统支持独立控制充电和放电MOS")
    print("- 支持同时控制充放电MOS")
    print("- 每次控制后会查询实时状态确认操作结果")
    print("- 控制操作需要key和clientId进行身份验证")
    print()

def analyze_data_structure():
    """分析数据结构"""
    print("📋 **核心数据结构分析**")
    print("-" * 50)
    
    print("🔋 **电池实时数据结构:**")
    print("- 基本信息: 电池ID、设备手机号、位置信息")
    print("- 电气参数: 总电压(84.40V)、总电流(0.00A)、SOC(96%)")
    print("- 温度信息: BMS温度(35°C)、2个温度传感器")
    print("- 电芯详情: 20个单体电压(4.220V-4.225V)")
    print("- 容量信息: 剩余容量(51.30Ah)、当前容量(53.00Ah)")
    print()
    
    print("⚡ **MOS状态信息:**")
    print("- chargeMosStatus: 充电MOS状态 (1=开启, 0=关闭)")
    print("- disChargeMosStatus: 放电MOS状态 (1=开启, 0=关闭)")
    print("- 状态位信息: 保护状态位、警告状态位、平衡状态位")
    print()
    
    print("🛡️ **保护和警告系统:**")
    print("- 过流保护: 充电过流、放电过流")
    print("- 温度保护: 高温、低温、BMS温度")
    print("- 电压保护: 过压、欠压、单体电压")
    print("- 系统保护: 短路、通信异常、BMS故障")
    print()

def analyze_authentication():
    """分析认证机制"""
    print("🔐 **认证和安全机制**")
    print("-" * 50)
    print("认证方式: JWT Token")
    print("Header: X-Access-Token")
    print("示例Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...")
    print()
    print("控制接口额外参数:")
    print("- key: 设备密钥 (79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c)")
    print("- clientId: 客户端ID (380074209785)")
    print("- cmdId: 控制命令ID")
    print()

def generate_flask_architecture():
    """生成Flask架构建议"""
    print("🏗️ **Flask项目架构建议**")
    print("-" * 50)
    print()
    print("📁 **项目结构:**")
    print("""
    lithium_monitor/
    ├── app.py                 # Flask主应用
    ├── config.py              # 配置文件
    ├── requirements.txt       # 依赖包
    ├── static/               # 静态文件
    │   ├── css/
    │   ├── js/
    │   └── images/
    ├── templates/            # HTML模板
    │   ├── base.html
    │   ├── dashboard.html
    │   └── control.html
    ├── api/                  # API模块
    │   ├── __init__.py
    │   ├── battery_data.py   # 电池数据API
    │   ├── control.py        # 控制API
    │   └── auth.py           # 认证模块
    ├── models/               # 数据模型
    │   ├── __init__.py
    │   ├── battery.py
    │   └── device.py
    └── utils/                # 工具函数
        ├── __init__.py
        ├── data_simulator.py # 数据模拟器
        └── helpers.py
    """)
    print()
    
    print("🎯 **核心功能模块:**")
    print("1. 数据获取层:")
    print("   - 模拟实时电池数据")
    print("   - 电池型号管理")
    print("   - 用户设备管理")
    print()
    print("2. 数据展示层:")
    print("   - 实时监控仪表板")
    print("   - 历史数据图表")
    print("   - 警告和保护状态显示")
    print()
    print("3. 控制请求层:")
    print("   - MOS开关控制")
    print("   - GPS定位控制")
    print("   - 设备参数配置")
    print()

if __name__ == "__main__":
    analyze_api_endpoints()
    analyze_control_sequence()
    analyze_data_structure()
    analyze_authentication()
    generate_flask_architecture()
    
    print("✅ **分析完成!**")
    print("现在可以开始构建Flask项目了。")
    print("建议下一步: 创建项目基础结构和数据模拟器。")
